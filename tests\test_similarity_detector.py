"""
图像相似度检测模块测试
"""

import pytest
import asyncio
import os
import cv2
import numpy as np

# 将项目根目录添加到Python路径
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.services.similarity_detector import SimilarityDetector
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

# 定义测试资源路径
TEST_IMAGE_FOLDER = "testimage"

@pytest.fixture(scope="module")
def event_loop():
    """为pytest-asyncio提供事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="module")
def detector():
    """初始化相似度检测器"""
    return SimilarityDetector()

@pytest.fixture(scope="module")
def test_images():
    """加载测试图片"""
    if not os.path.exists(TEST_IMAGE_FOLDER):
        pytest.skip(f"测试图片文件夹不存在: {TEST_IMAGE_FOLDER}")
    
    images = {}
    for filename in os.listdir(TEST_IMAGE_FOLDER):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            path = os.path.join(TEST_IMAGE_FOLDER, filename)
            img = cv2.imread(path)
            if img is not None:
                images[filename] = img
    
    if len(images) < 2:
        pytest.skip("测试图片数量不足，至少需要2张")
        
    return images

@pytest.mark.asyncio
async def test_scan_folder_and_group_similar(detector: SimilarityDetector):
    """测试文件夹扫描和相似度分组功能 - 全量相似度分析"""
    logger.info("开始测试文件夹扫描和相似度分组功能")

    # 确保测试文件夹存在
    if not os.path.exists(TEST_IMAGE_FOLDER) or not os.listdir(TEST_IMAGE_FOLDER):
        pytest.skip(f"测试文件夹 '{TEST_IMAGE_FOLDER}' 为空或不存在")

    # 使用更低的阈值来看到更多相似关系
    result = await detector.scan_folder_and_group_similar(
        folder_path=TEST_IMAGE_FOLDER,
        method="hybrid",
        threshold=0.6,  # 降低阈值以看到更多相似关系
        options={
            'sift_ratio_threshold': 0.75,
            'sift_min_match_count': 8,
            'orb_max_distance': 50,
            'orb_min_match_count': 8
        }
    )

    logger.info(f"扫描结果: {result}")

    assert "error" not in result, f"扫描文件夹时出错: {result.get('error')}"
    assert "total_images" in result
    assert "similar_groups" in result
    assert "statistics" in result
    assert result["total_images"] > 0

    # 详细打印全量相似度分析结果
    print(f"\n{'='*80}")
    print(f"📊 全量相似度分析报告")
    print(f"{'='*80}")
    print(f"📁 文件夹: {result['folder_path']}")
    print(f"🖼️  总图片数: {result['total_images']}")
    print(f"👥 相似组数: {result['statistics']['similar_groups_count']}")
    print(f"📈 平均相似度: {result['statistics']['avg_similarity']:.4f}")
    print(f"📊 最高相似度: {result['statistics']['max_similarity']:.4f}")
    print(f"📉 最低相似度: {result['statistics']['min_similarity']:.4f}")
    print(f"🎯 使用阈值: {result['statistics']['threshold_used']}")
    print(f"🔧 检测方法: {result['statistics']['method_used']}")

    # 打印所有图片信息
    print(f"\n📋 图片列表:")
    for img_info in result['image_info']:
        print(f"  {img_info['index']:2d}. {img_info['filename']} ({img_info['size'][0]}x{img_info['size'][1]})")

    # 打印相似度矩阵（只显示上三角）
    print(f"\n🔍 相似度矩阵 (阈值: {result['statistics']['threshold_used']}):")
    similarity_matrix = result['similarity_matrix']
    n = len(similarity_matrix)

    # 打印表头
    print("     ", end="")
    for j in range(n):
        print(f"{j:6d}", end="")
    print()

    # 打印矩阵
    for i in range(n):
        print(f"{i:2d}: ", end="")
        for j in range(n):
            if i == j:
                print("  1.00", end="")
            elif i < j:
                sim = similarity_matrix[i][j]
                if sim >= result['statistics']['threshold_used']:
                    print(f"\033[92m{sim:6.3f}\033[0m", end="")  # 绿色显示超过阈值的
                else:
                    print(f"{sim:6.3f}", end="")
            else:
                print("      ", end="")  # 下三角留空
        print()

    # 打印相似组详情
    if result["similar_groups"]:
        print(f"\n👥 相似组详情:")
        for i, group in enumerate(result["similar_groups"]):
            print(f"\n  组 {i+1} (共{group['size']}张图片):")
            print(f"    📊 平均相似度: {group['avg_similarity']:.4f}")
            print(f"    📈 最高相似度: {group['max_similarity']:.4f}")
            print(f"    📉 最低相似度: {group['min_similarity']:.4f}")
            print(f"    📁 文件:")
            for j, filename in enumerate(group['files']):
                idx = group['indices'][j]
                print(f"      {idx:2d}. {filename}")

            # 显示组内详细相似度
            print(f"    🔗 组内相似度关系:")
            indices = group['indices']
            for idx1 in indices:
                for idx2 in indices:
                    if idx1 < idx2:
                        sim = similarity_matrix[idx1][idx2]
                        file1 = result['image_info'][idx1]['filename']
                        file2 = result['image_info'][idx2]['filename']
                        print(f"      {file1} ↔ {file2}: {sim:.4f}")
    else:
        print(f"\n❌ 在阈值 {result['statistics']['threshold_used']} 下未发现相似组")

    # 基本断言
    if result["similar_groups"]:
        for group in result["similar_groups"]:
            assert "files" in group
            assert "avg_similarity" in group
            assert len(group["files"]) > 1

@pytest.mark.asyncio
@pytest.mark.parametrize("method", ["hash", "histogram", "sift", "orb", "hybrid"])
async def test_detect_similarity_methods(detector: SimilarityDetector, test_images, method):
    """测试不同的相似度检测方法"""
    logger.info(f"测试相似度检测方法: {method}")
    
    # 从测试图片中随机选择两张
    img_keys = list(test_images.keys())
    image1 = test_images[img_keys[0]]
    image2 = test_images[img_keys[1]]

    result = await detector.detect_similarity(image1, image2, method=method)
    
    logger.info(f"'{method}' 方法检测结果: {result}")

    assert isinstance(result, dict)
    if method == "hybrid":
        assert "overall_similarity" in result
        assert 0 <= result["overall_similarity"] <= 1
    else:
        key = f"{method}_similarity"
        assert key in result
        assert "similarity" in result[key]
        assert 0 <= result[key]["similarity"] <= 1


@pytest.mark.asyncio
async def test_identical_images_similarity(detector: SimilarityDetector, test_images):
    """测试相同图片的相似度"""
    logger.info("测试相同图片的相似度")
    img_key = list(test_images.keys())[0]
    image = test_images[img_key]

    result = await detector.detect_similarity(image, image.copy(), method="hybrid")
    
    logger.info(f"相同图片检测结果: {result}")
    
    assert result["overall_similarity"] > 0.98, "相同图片的相似度应该非常高"

def create_blank_image(width, height, color=(255, 255, 255)):
    """创建一个纯色图像"""
    img = np.full((height, width, 3), color, dtype=np.uint8)
    return img

@pytest.mark.asyncio
async def test_completely_different_images_similarity(detector: SimilarityDetector):
    """测试完全不同图片的相似度（如黑白图片）"""
    logger.info("测试完全不同图片的相似度")
    img_black = create_blank_image(256, 256, color=(0, 0, 0))
    img_white = create_blank_image(256, 256, color=(255, 255, 255))
    
    result = await detector.detect_similarity(img_black, img_white, method="hybrid")
    
    logger.info(f"黑白图片检测结果: {result}")
    
    assert result["overall_similarity"] < 0.2, "完全不同的图片相似度应该非常低"

@pytest.mark.asyncio
async def test_non_existent_folder(detector: SimilarityDetector):
    """测试不存在的文件夹路径"""
    logger.info("测试不存在的文件夹")
    non_existent_folder = "non_existent_folder_xyz"
    result = await detector.scan_folder_and_group_similar(non_existent_folder)
    
    logger.info(f"不存在文件夹的测试结果: {result}")
    
    assert "error" in result
    assert "文件夹不存在" in result["error"]

@pytest.mark.asyncio
async def test_empty_folder(detector: SimilarityDetector):
    """测试空文件夹"""
    logger.info("测试空文件夹")
    empty_folder = "empty_test_folder"
    os.makedirs(empty_folder, exist_ok=True)

    result = await detector.scan_folder_and_group_similar(empty_folder)

    logger.info(f"空文件夹测试结果: {result}")

    assert "error" in result
    assert "图片数量不足" in result["error"]

    # 清理创建的空文件夹
    os.rmdir(empty_folder)

@pytest.mark.asyncio
async def test_specific_similar_images_123(detector: SimilarityDetector):
    """专门测试1、2、3图片的相似度 - 2是剪切的1，3是旋转的1"""
    logger.info("开始测试1、2、3图片的相似度")

    # 确保测试文件夹存在
    if not os.path.exists(TEST_IMAGE_FOLDER):
        pytest.skip(f"测试文件夹不存在: {TEST_IMAGE_FOLDER}")

    # 查找1.png, 2.png, 3.png
    target_files = ['1.png', '2.png', '3.png']
    found_files = {}

    for filename in os.listdir(TEST_IMAGE_FOLDER):
        if filename in target_files:
            file_path = os.path.join(TEST_IMAGE_FOLDER, filename)
            image = cv2.imread(file_path)
            if image is not None:
                found_files[filename] = {
                    'path': file_path,
                    'image': image,
                    'size': image.shape[:2]
                }

    if len(found_files) < 3:
        pytest.skip(f"未找到完整的1.png, 2.png, 3.png文件，只找到: {list(found_files.keys())}")

    print(f"\n🔍 专门测试1、2、3图片相似度:")
    print(f"  1.png: {found_files['1.png']['size']}")
    print(f"  2.png: {found_files['2.png']['size']} (剪切版本)")
    print(f"  3.png: {found_files['3.png']['size']} (旋转版本)")

    # 测试各种方法的相似度
    methods = ["hash", "histogram", "sift", "orb", "hybrid"]

    for method in methods:
        print(f"\n📊 {method.upper()} 方法结果:")

        # 1 vs 2 (剪切)
        result_12 = await detector.detect_similarity(
            found_files['1.png']['image'],
            found_files['2.png']['image'],
            method=method,
            options={
                'sift_ratio_threshold': 0.8,
                'sift_min_match_count': 10,
                'orb_max_distance': 50,
                'orb_min_match_count': 10
            }
        )

        # 1 vs 3 (旋转)
        result_13 = await detector.detect_similarity(
            found_files['1.png']['image'],
            found_files['3.png']['image'],
            method=method,
            options={
                'sift_ratio_threshold': 0.8,
                'sift_min_match_count': 10,
                'orb_max_distance': 50,
                'orb_min_match_count': 10
            }
        )

        # 2 vs 3 (剪切 vs 旋转)
        result_23 = await detector.detect_similarity(
            found_files['2.png']['image'],
            found_files['3.png']['image'],
            method=method,
            options={
                'sift_ratio_threshold': 0.8,
                'sift_min_match_count': 10,
                'orb_max_distance': 50,
                'orb_min_match_count': 10
            }
        )

        # 提取相似度值
        if method == "hybrid":
            sim_12 = result_12.get("overall_similarity", 0)
            sim_13 = result_13.get("overall_similarity", 0)
            sim_23 = result_23.get("overall_similarity", 0)
        else:
            key = f"{method}_similarity"
            sim_12 = result_12.get(key, {}).get("similarity", 0)
            sim_13 = result_13.get(key, {}).get("similarity", 0)
            sim_23 = result_23.get(key, {}).get("similarity", 0)

        print(f"  1.png ↔ 2.png (剪切): {sim_12:.4f}")
        print(f"  1.png ↔ 3.png (旋转): {sim_13:.4f}")
        print(f"  2.png ↔ 3.png (剪切↔旋转): {sim_23:.4f}")

        # 显示详细信息
        if method == "sift":
            sift_12 = result_12.get("sift_similarity", {})
            print(f"    SIFT详情 1↔2: 匹配数={sift_12.get('good_matches', 0)}, 特征点1={sift_12.get('keypoints1', 0)}, 特征点2={sift_12.get('keypoints2', 0)}")
        elif method == "orb":
            orb_12 = result_12.get("orb_similarity", {})
            print(f"    ORB详情 1↔2: 匹配数={orb_12.get('good_matches', 0)}, 特征点1={orb_12.get('keypoints1', 0)}, 特征点2={orb_12.get('keypoints2', 0)}")

    # 基本断言 - 剪切和旋转的图片应该有一定相似度
    hybrid_result_12 = await detector.detect_similarity(
        found_files['1.png']['image'],
        found_files['2.png']['image'],
        method="hybrid"
    )

    hybrid_result_13 = await detector.detect_similarity(
        found_files['1.png']['image'],
        found_files['3.png']['image'],
        method="hybrid"
    )

    sim_12_hybrid = hybrid_result_12.get("overall_similarity", 0)
    sim_13_hybrid = hybrid_result_13.get("overall_similarity", 0)

    print(f"\n🎯 期望结果分析:")
    print(f"  剪切相似度 (1↔2): {sim_12_hybrid:.4f} - {'✅ 检测到' if sim_12_hybrid > 0.6 else '❌ 未检测到'}")
    print(f"  旋转相似度 (1↔3): {sim_13_hybrid:.4f} - {'✅ 检测到' if sim_13_hybrid > 0.6 else '❌ 未检测到'}")

    # 基本断言
    assert sim_12_hybrid > 0, "剪切图片应该有一定相似度"
    assert sim_13_hybrid > 0, "旋转图片应该有一定相似度"

@pytest.mark.asyncio
async def test_pairwise_similarity_matrix(detector: SimilarityDetector):
    """测试两两相似度矩阵 - 验证矩阵的正确性"""
    logger.info("开始两两相似度矩阵测试")

    # 确保测试文件夹存在
    if not os.path.exists(TEST_IMAGE_FOLDER) or not os.listdir(TEST_IMAGE_FOLDER):
        pytest.skip(f"测试文件夹 '{TEST_IMAGE_FOLDER}' 为空或不存在")

    result = await detector.scan_folder_and_group_similar(
        folder_path=TEST_IMAGE_FOLDER,
        method="hybrid",
        threshold=0.5,
        options={
            'sift_ratio_threshold': 0.8,
            'sift_min_match_count': 3,
            'orb_max_distance': 80,
            'orb_min_match_count': 3
        }
    )

    assert "error" not in result, f"扫描失败: {result.get('error')}"

    similarity_matrix = result['similarity_matrix']
    image_info = result['image_info']
    n = len(similarity_matrix)

    # 验证矩阵是对称的
    for i in range(n):
        for j in range(n):
            assert abs(similarity_matrix[i][j] - similarity_matrix[j][i]) < 1e-6, \
                f"相似度矩阵应该是对称的: matrix[{i}][{j}] != matrix[{j}][{i}]"

    # 验证对角线都是1.0
    for i in range(n):
        assert abs(similarity_matrix[i][i] - 1.0) < 1e-6, \
            f"对角线元素应该是1.0: matrix[{i}][{i}] = {similarity_matrix[i][i]}"

    # 创建相似度对列表并找出最相似的
    pairs = []
    for i in range(n):
        for j in range(i+1, n):
            pairs.append({
                'file1': image_info[i]['filename'],
                'file2': image_info[j]['filename'],
                'similarity': similarity_matrix[i][j]
            })

    pairs.sort(key=lambda x: x['similarity'], reverse=True)

    # 打印最相似的前10对供人工检查
    print(f"\n🏆 最相似的图片对 (TOP 10):")
    for i, pair in enumerate(pairs[:10], 1):
        print(f"  {i:2d}. {pair['file1']} ↔ {pair['file2']}: {pair['similarity']:.4f}")

    # 统计相似度分布
    high_similarity = sum(1 for p in pairs if p['similarity'] >= 0.8)
    medium_similarity = sum(1 for p in pairs if 0.6 <= p['similarity'] < 0.8)
    low_similarity = sum(1 for p in pairs if p['similarity'] < 0.6)

    print(f"\n📊 相似度分布:")
    print(f"  高度相似 (≥0.8): {high_similarity} 对")
    print(f"  中度相似 (0.6-0.8): {medium_similarity} 对")
    print(f"  低度相似 (<0.6): {low_similarity} 对")

    # 基本断言
    assert len(pairs) > 0, "应该有图片对"
    assert all(0 <= pair['similarity'] <= 1 for pair in pairs), "相似度应该在0-1之间"


if __name__ == "__main__":
    # 运行pytest测试
    pytest.main([__file__, "-v", "-s"])