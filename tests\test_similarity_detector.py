"""
图像相似度检测模块测试
"""

import pytest
import asyncio
import os
import cv2
import numpy as np

# 将项目根目录添加到Python路径
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.services.similarity_detector import SimilarityDetector
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

# 定义测试资源路径
TEST_IMAGE_FOLDER = "testimage"

@pytest.fixture(scope="module")
def event_loop():
    """为pytest-asyncio提供事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="module")
def detector():
    """初始化相似度检测器"""
    return SimilarityDetector()

@pytest.fixture(scope="module")
def test_images():
    """加载测试图片"""
    if not os.path.exists(TEST_IMAGE_FOLDER):
        pytest.skip(f"测试图片文件夹不存在: {TEST_IMAGE_FOLDER}")
    
    images = {}
    for filename in os.listdir(TEST_IMAGE_FOLDER):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            path = os.path.join(TEST_IMAGE_FOLDER, filename)
            img = cv2.imread(path)
            if img is not None:
                images[filename] = img
    
    if len(images) < 2:
        pytest.skip("测试图片数量不足，至少需要2张")
        
    return images

@pytest.mark.asyncio
async def test_scan_folder_and_group_similar(detector: SimilarityDetector):
    """测试文件夹扫描和相似度分组功能"""
    logger.info("开始测试文件夹扫描和相似度分组功能")
    
    # 确保测试文件夹存在
    if not os.path.exists(TEST_IMAGE_FOLDER) or not os.listdir(TEST_IMAGE_FOLDER):
        pytest.skip(f"测试文件夹 '{TEST_IMAGE_FOLDER}' 为空或不存在")

    result = await detector.scan_folder_and_group_similar(
        folder_path=TEST_IMAGE_FOLDER,
        method="hybrid",
        threshold=0.8
    )

    logger.info(f"扫描结果: {result}")

    assert "error" not in result, f"扫描文件夹时出错: {result.get('error')}"
    assert "total_images" in result
    assert "similar_groups" in result
    assert "statistics" in result
    assert result["total_images"] > 0

    if result["similar_groups"]:
        for group in result["similar_groups"]:
            assert "files" in group
            assert "avg_similarity" in group
            assert len(group["files"]) > 1

@pytest.mark.asyncio
@pytest.mark.parametrize("method", ["hash", "histogram", "sift", "orb", "hybrid"])
async def test_detect_similarity_methods(detector: SimilarityDetector, test_images, method):
    """测试不同的相似度检测方法"""
    logger.info(f"测试相似度检测方法: {method}")
    
    # 从测试图片中随机选择两张
    img_keys = list(test_images.keys())
    image1 = test_images[img_keys[0]]
    image2 = test_images[img_keys[1]]

    result = await detector.detect_similarity(image1, image2, method=method)
    
    logger.info(f"'{method}' 方法检测结果: {result}")

    assert isinstance(result, dict)
    if method == "hybrid":
        assert "overall_similarity" in result
        assert 0 <= result["overall_similarity"] <= 1
    else:
        key = f"{method}_similarity"
        assert key in result
        assert "similarity" in result[key]
        assert 0 <= result[key]["similarity"] <= 1


@pytest.mark.asyncio
async def test_identical_images_similarity(detector: SimilarityDetector, test_images):
    """测试相同图片的相似度"""
    logger.info("测试相同图片的相似度")
    img_key = list(test_images.keys())[0]
    image = test_images[img_key]

    result = await detector.detect_similarity(image, image.copy(), method="hybrid")
    
    logger.info(f"相同图片检测结果: {result}")
    
    assert result["overall_similarity"] > 0.98, "相同图片的相似度应该非常高"

def create_blank_image(width, height, color=(255, 255, 255)):
    """创建一个纯色图像"""
    img = np.full((height, width, 3), color, dtype=np.uint8)
    return img

@pytest.mark.asyncio
async def test_completely_different_images_similarity(detector: SimilarityDetector):
    """测试完全不同图片的相似度（如黑白图片）"""
    logger.info("测试完全不同图片的相似度")
    img_black = create_blank_image(256, 256, color=(0, 0, 0))
    img_white = create_blank_image(256, 256, color=(255, 255, 255))
    
    result = await detector.detect_similarity(img_black, img_white, method="hybrid")
    
    logger.info(f"黑白图片检测结果: {result}")
    
    assert result["overall_similarity"] < 0.2, "完全不同的图片相似度应该非常低"

@pytest.mark.asyncio
async def test_non_existent_folder(detector: SimilarityDetector):
    """测试不存在的文件夹路径"""
    logger.info("测试不存在的文件夹")
    non_existent_folder = "non_existent_folder_xyz"
    result = await detector.scan_folder_and_group_similar(non_existent_folder)
    
    logger.info(f"不存在文件夹的测试结果: {result}")
    
    assert "error" in result
    assert "文件夹不存在" in result["error"]

@pytest.empyt_folder
async def test_empty_folder(detector: SimilarityDetector):
    """测试空文件夹"""
    logger.info("测试空文件夹")
    empty_folder = "empty_test_folder"
    os.makedirs(empty_folder, exist_ok=True)
    
    result = await detector.scan_folder_and_group_similar(empty_folder)
    
    logger.info(f"空文件夹测试结果: {result}")
    
    assert "error" in result
    assert "图片数量不足" in result["error"]
    
    # 清理创建的空文件夹
    os.rmdir(empty_folder) 