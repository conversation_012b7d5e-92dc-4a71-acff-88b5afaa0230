"""
图像相似度检测模块测试
"""

import pytest
import asyncio
import os
import cv2
import numpy as np

# 将项目根目录添加到Python路径
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.services.similarity_detector import SimilarityDetector
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

# 定义测试资源路径
TEST_IMAGE_FOLDER = "testimage"

@pytest.fixture(scope="module")
def event_loop():
    """为pytest-asyncio提供事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="module")
def detector():
    """初始化相似度检测器"""
    return SimilarityDetector()

@pytest.fixture(scope="module")
def test_images():
    """加载测试图片"""
    if not os.path.exists(TEST_IMAGE_FOLDER):
        pytest.skip(f"测试图片文件夹不存在: {TEST_IMAGE_FOLDER}")
    
    images = {}
    for filename in os.listdir(TEST_IMAGE_FOLDER):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            path = os.path.join(TEST_IMAGE_FOLDER, filename)
            img = cv2.imread(path)
            if img is not None:
                images[filename] = img
    
    if len(images) < 2:
        pytest.skip("测试图片数量不足，至少需要2张")
        
    return images

@pytest.mark.asyncio
async def test_scan_folder_and_group_similar(detector: SimilarityDetector):
    """测试文件夹扫描和相似度分组功能 - 全量相似度分析"""
    logger.info("开始测试文件夹扫描和相似度分组功能")

    # 确保测试文件夹存在
    if not os.path.exists(TEST_IMAGE_FOLDER) or not os.listdir(TEST_IMAGE_FOLDER):
        pytest.skip(f"测试文件夹 '{TEST_IMAGE_FOLDER}' 为空或不存在")

    # 使用更低的阈值来看到更多相似关系
    result = await detector.scan_folder_and_group_similar(
        folder_path=TEST_IMAGE_FOLDER,
        method="hybrid",
        threshold=0.6,  # 降低阈值以看到更多相似关系
        options={
            'sift_ratio_threshold': 0.75,
            'sift_min_match_count': 8,
            'orb_max_distance': 50,
            'orb_min_match_count': 8
        }
    )

    logger.info(f"扫描结果: {result}")

    assert "error" not in result, f"扫描文件夹时出错: {result.get('error')}"
    assert "total_images" in result
    assert "similar_groups" in result
    assert "statistics" in result
    assert result["total_images"] > 0

    # 详细打印全量相似度分析结果
    print(f"\n{'='*80}")
    print(f"📊 全量相似度分析报告")
    print(f"{'='*80}")
    print(f"📁 文件夹: {result['folder_path']}")
    print(f"🖼️  总图片数: {result['total_images']}")
    print(f"👥 相似组数: {result['statistics']['similar_groups_count']}")
    print(f"📈 平均相似度: {result['statistics']['avg_similarity']:.4f}")
    print(f"📊 最高相似度: {result['statistics']['max_similarity']:.4f}")
    print(f"📉 最低相似度: {result['statistics']['min_similarity']:.4f}")
    print(f"🎯 使用阈值: {result['statistics']['threshold_used']}")
    print(f"🔧 检测方法: {result['statistics']['method_used']}")

    # 打印所有图片信息
    print(f"\n📋 图片列表:")
    for img_info in result['image_info']:
        print(f"  {img_info['index']:2d}. {img_info['filename']} ({img_info['size'][0]}x{img_info['size'][1]})")

    # 打印相似度矩阵（只显示上三角）
    print(f"\n🔍 相似度矩阵 (阈值: {result['statistics']['threshold_used']}):")
    similarity_matrix = result['similarity_matrix']
    n = len(similarity_matrix)

    # 打印表头
    print("     ", end="")
    for j in range(n):
        print(f"{j:6d}", end="")
    print()

    # 打印矩阵
    for i in range(n):
        print(f"{i:2d}: ", end="")
        for j in range(n):
            if i == j:
                print("  1.00", end="")
            elif i < j:
                sim = similarity_matrix[i][j]
                if sim >= result['statistics']['threshold_used']:
                    print(f"\033[92m{sim:6.3f}\033[0m", end="")  # 绿色显示超过阈值的
                else:
                    print(f"{sim:6.3f}", end="")
            else:
                print("      ", end="")  # 下三角留空
        print()

    # 打印相似组详情
    if result["similar_groups"]:
        print(f"\n👥 相似组详情:")
        for i, group in enumerate(result["similar_groups"]):
            print(f"\n  组 {i+1} (共{group['size']}张图片):")
            print(f"    📊 平均相似度: {group['avg_similarity']:.4f}")
            print(f"    📈 最高相似度: {group['max_similarity']:.4f}")
            print(f"    📉 最低相似度: {group['min_similarity']:.4f}")
            print(f"    📁 文件:")
            for j, filename in enumerate(group['files']):
                idx = group['indices'][j]
                print(f"      {idx:2d}. {filename}")

            # 显示组内详细相似度
            print(f"    🔗 组内相似度关系:")
            indices = group['indices']
            for idx1 in indices:
                for idx2 in indices:
                    if idx1 < idx2:
                        sim = similarity_matrix[idx1][idx2]
                        file1 = result['image_info'][idx1]['filename']
                        file2 = result['image_info'][idx2]['filename']
                        print(f"      {file1} ↔ {file2}: {sim:.4f}")
    else:
        print(f"\n❌ 在阈值 {result['statistics']['threshold_used']} 下未发现相似组")

    # 基本断言
    if result["similar_groups"]:
        for group in result["similar_groups"]:
            assert "files" in group
            assert "avg_similarity" in group
            assert len(group["files"]) > 1

@pytest.mark.asyncio
@pytest.mark.parametrize("method", ["hash", "histogram", "sift", "orb", "hybrid"])
async def test_detect_similarity_methods(detector: SimilarityDetector, test_images, method):
    """测试不同的相似度检测方法"""
    logger.info(f"测试相似度检测方法: {method}")
    
    # 从测试图片中随机选择两张
    img_keys = list(test_images.keys())
    image1 = test_images[img_keys[0]]
    image2 = test_images[img_keys[1]]

    result = await detector.detect_similarity(image1, image2, method=method)
    
    logger.info(f"'{method}' 方法检测结果: {result}")

    assert isinstance(result, dict)
    if method == "hybrid":
        assert "overall_similarity" in result
        assert 0 <= result["overall_similarity"] <= 1
    else:
        key = f"{method}_similarity"
        assert key in result
        assert "similarity" in result[key]
        assert 0 <= result[key]["similarity"] <= 1


@pytest.mark.asyncio
async def test_identical_images_similarity(detector: SimilarityDetector, test_images):
    """测试相同图片的相似度"""
    logger.info("测试相同图片的相似度")
    img_key = list(test_images.keys())[0]
    image = test_images[img_key]

    result = await detector.detect_similarity(image, image.copy(), method="hybrid")
    
    logger.info(f"相同图片检测结果: {result}")
    
    assert result["overall_similarity"] > 0.98, "相同图片的相似度应该非常高"

def create_blank_image(width, height, color=(255, 255, 255)):
    """创建一个纯色图像"""
    img = np.full((height, width, 3), color, dtype=np.uint8)
    return img

@pytest.mark.asyncio
async def test_completely_different_images_similarity(detector: SimilarityDetector):
    """测试完全不同图片的相似度（如黑白图片）"""
    logger.info("测试完全不同图片的相似度")
    img_black = create_blank_image(256, 256, color=(0, 0, 0))
    img_white = create_blank_image(256, 256, color=(255, 255, 255))
    
    result = await detector.detect_similarity(img_black, img_white, method="hybrid")
    
    logger.info(f"黑白图片检测结果: {result}")
    
    assert result["overall_similarity"] < 0.2, "完全不同的图片相似度应该非常低"

@pytest.mark.asyncio
async def test_non_existent_folder(detector: SimilarityDetector):
    """测试不存在的文件夹路径"""
    logger.info("测试不存在的文件夹")
    non_existent_folder = "non_existent_folder_xyz"
    result = await detector.scan_folder_and_group_similar(non_existent_folder)
    
    logger.info(f"不存在文件夹的测试结果: {result}")
    
    assert "error" in result
    assert "文件夹不存在" in result["error"]

@pytest.mark.asyncio
async def test_empty_folder(detector: SimilarityDetector):
    """测试空文件夹"""
    logger.info("测试空文件夹")
    empty_folder = "empty_test_folder"
    os.makedirs(empty_folder, exist_ok=True)

    result = await detector.scan_folder_and_group_similar(empty_folder)

    logger.info(f"空文件夹测试结果: {result}")

    assert "error" in result
    assert "图片数量不足" in result["error"]

    # 清理创建的空文件夹
    os.rmdir(empty_folder)

@pytest.mark.asyncio
async def test_comprehensive_similarity_analysis(detector: SimilarityDetector):
    """全量相似度分析测试 - 详细显示每对图片的相似度"""
    logger.info("开始全量相似度分析测试")

    # 确保测试文件夹存在
    if not os.path.exists(TEST_IMAGE_FOLDER) or not os.listdir(TEST_IMAGE_FOLDER):
        pytest.skip(f"测试文件夹 '{TEST_IMAGE_FOLDER}' 为空或不存在")

    # 多个阈值测试，看不同阈值下的分组情况
    thresholds = [0.5, 0.6, 0.7, 0.8, 0.9]

    print(f"\n{'='*100}")
    print(f"🔍 全量相似度分析 - 多阈值对比")
    print(f"{'='*100}")

    for threshold in thresholds:
        print(f"\n🎯 阈值: {threshold}")
        print(f"{'-'*50}")

        result = await detector.scan_folder_and_group_similar(
            folder_path=TEST_IMAGE_FOLDER,
            method="hybrid",
            threshold=threshold,
            options={
                'sift_ratio_threshold': 0.75,
                'sift_min_match_count': 5,
                'orb_max_distance': 60,
                'orb_min_match_count': 5
            }
        )

        if "error" in result:
            print(f"❌ 错误: {result['error']}")
            continue

        print(f"📊 统计: {result['statistics']['similar_groups_count']} 个相似组")

        if result["similar_groups"]:
            for i, group in enumerate(result["similar_groups"]):
                files_str = ", ".join([f"{idx}:{name}" for idx, name in zip(group['indices'], group['files'])])
                print(f"  组{i+1}: [{files_str}] (平均相似度: {group['avg_similarity']:.3f})")
        else:
            print(f"  无相似组")

@pytest.mark.asyncio
async def test_pairwise_similarity_matrix(detector: SimilarityDetector):
    """测试两两相似度矩阵 - 显示所有图片对的详细相似度"""
    logger.info("开始两两相似度矩阵测试")

    # 确保测试文件夹存在
    if not os.path.exists(TEST_IMAGE_FOLDER) or not os.listdir(TEST_IMAGE_FOLDER):
        pytest.skip(f"测试文件夹 '{TEST_IMAGE_FOLDER}' 为空或不存在")

    result = await detector.scan_folder_and_group_similar(
        folder_path=TEST_IMAGE_FOLDER,
        method="hybrid",
        threshold=0.5,  # 使用较低阈值以显示更多关系
        options={
            'sift_ratio_threshold': 0.8,
            'sift_min_match_count': 3,
            'orb_max_distance': 80,
            'orb_min_match_count': 3
        }
    )

    if "error" in result:
        pytest.skip(f"扫描失败: {result['error']}")

    print(f"\n{'='*120}")
    print(f"📊 详细两两相似度分析")
    print(f"{'='*120}")

    similarity_matrix = result['similarity_matrix']
    image_info = result['image_info']
    n = len(similarity_matrix)

    # 创建所有图片对的相似度列表
    pairs = []
    for i in range(n):
        for j in range(i+1, n):
            pairs.append({
                'i': i,
                'j': j,
                'file1': image_info[i]['filename'],
                'file2': image_info[j]['filename'],
                'similarity': similarity_matrix[i][j]
            })

    # 按相似度排序
    pairs.sort(key=lambda x: x['similarity'], reverse=True)

    print(f"🔍 所有图片对相似度 (按相似度降序排列):")
    print(f"{'序号':<4} {'图片1':<20} {'图片2':<20} {'相似度':<8} {'关系'}")
    print(f"{'-'*70}")

    for idx, pair in enumerate(pairs, 1):
        similarity = pair['similarity']
        if similarity >= 0.8:
            relation = "🟢 高度相似"
        elif similarity >= 0.6:
            relation = "🟡 中度相似"
        elif similarity >= 0.4:
            relation = "🟠 低度相似"
        else:
            relation = "🔴 不相似"

        print(f"{idx:<4} {pair['file1']:<20} {pair['file2']:<20} {similarity:<8.4f} {relation}")

    # 显示最相似的前5对
    print(f"\n🏆 最相似的图片对 (TOP 5):")
    for i, pair in enumerate(pairs[:5], 1):
        print(f"  {i}. {pair['file1']} ↔ {pair['file2']}: {pair['similarity']:.4f}")

    # 显示最不相似的后5对
    print(f"\n🔻 最不相似的图片对 (BOTTOM 5):")
    for i, pair in enumerate(pairs[-5:], 1):
        print(f"  {i}. {pair['file1']} ↔ {pair['file2']}: {pair['similarity']:.4f}")

    # 基本断言
    assert len(pairs) > 0
    assert all(0 <= pair['similarity'] <= 1 for pair in pairs)


if __name__ == "__main__":
    """直接运行全量相似度分析"""
    import asyncio

    async def run_full_analysis():
        """运行完整的相似度分析"""
        detector = SimilarityDetector()

        print(f"🚀 开始全量相似度分析...")
        print(f"📁 测试文件夹: {TEST_IMAGE_FOLDER}")

        if not os.path.exists(TEST_IMAGE_FOLDER):
            print(f"❌ 测试文件夹不存在: {TEST_IMAGE_FOLDER}")
            return

        # 运行全量分析
        await test_comprehensive_similarity_analysis(detector)

        print(f"\n" + "="*100)

        # 运行详细两两分析
        await test_pairwise_similarity_matrix(detector)

        print(f"\n✅ 全量相似度分析完成!")

    # 运行分析
    asyncio.run(run_full_analysis())