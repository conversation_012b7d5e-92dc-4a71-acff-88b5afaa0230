"""
折角检测和修复功能测试
"""

import pytest
import asyncio
import numpy as np
import cv2
from pathlib import Path
from app.services.corner_corrector import CornerCorrector


class TestCornerCorrector:
    """折角检测和修复测试类"""
    
    @pytest.fixture
    def corrector(self):
        """创建折角修复器实例"""
        return CornerCorrector()
    
    @pytest.fixture
    def test_image_path(self):
        """测试图像路径"""
        return "testimage/16.jpg"  # 使用现有的测试图像
    
    @pytest.mark.asyncio
    async def test_detect_corners(self, corrector, test_image_path):
        """测试折角检测功能"""
        result = await corrector.detect(test_image_path)
        
        # 验证返回结果结构
        assert "has_folded_corners" in result
        assert "folded_corners" in result
        assert "confidence" in result
        assert "needs_correction" in result
        
        # 验证结果类型
        assert isinstance(result["has_folded_corners"], bool)
        assert isinstance(result["folded_corners"], list)
        assert isinstance(result["confidence"], float)
        assert isinstance(result["needs_correction"], bool)
        
        print(f"\n折角检测结果:")
        print(f"  文件: {result.get('file_path', 'N/A')}")
        print(f"  有折角: {result['has_folded_corners']}")
        print(f"  折角数量: {len(result['folded_corners'])}")
        print(f"  置信度: {result['confidence']:.3f}")
        print(f"  需要修复: {result['needs_correction']}")
    
    @pytest.mark.asyncio
    async def test_correct_corners(self, corrector, test_image_path):
        """测试折角修复功能"""
        result = await corrector.correct(test_image_path)
        
        # 验证返回结果结构
        assert "file_path" in result
        assert "correction_applied" in result
        assert "detected_corners" in result
        assert "confidence" in result
        
        print(f"\n折角修复结果:")
        print(f"  原文件: {result.get('file_path', 'N/A')}")
        print(f"  修复应用: {result['correction_applied']}")
        print(f"  检测到的折角: {len(result['detected_corners'])}")
        print(f"  置信度: {result['confidence']:.3f}")
        
        if result["correction_applied"]:
            assert "corrected_file" in result
            corrected_file = result["corrected_file"]
            assert Path(corrected_file).exists()
            print(f"  修复文件: {corrected_file}")
    
    @pytest.mark.asyncio
    async def test_folder_detect(self, corrector):
        """测试文件夹折角检测功能"""
        result = await corrector.detect("testimage")
        
        # 验证返回结果结构
        assert "total_files" in result
        assert "folded_count" in result
        assert "folded_files" in result
        assert "normal_files" in result
        assert "error_files" in result
        
        # 验证处理了文件
        assert result["total_files"] > 0
        assert result["folded_count"] >= 0
        
        print(f"\n文件夹检测结果:")
        print(f"  总文件数: {result['total_files']}")
        print(f"  折角文件数: {result['folded_count']}")
        print(f"  正常文件数: {len(result['normal_files'])}")
        print(f"  错误文件数: {len(result['error_files'])}")
    
    def test_angle_calculation(self, corrector):
        """测试角度计算功能"""
        # 测试直角
        p1 = np.array([0, 0])
        p2 = np.array([1, 0])
        p3 = np.array([1, 1])
        angle = corrector._calculate_angle(p1, p2, p3)
        assert abs(angle - 90.0) < 1.0, f"直角计算错误: {angle}"
        
        # 测试锐角
        p1 = np.array([0, 0])
        p2 = np.array([1, 0])
        p3 = np.array([0.5, 0.5])
        angle = corrector._calculate_angle(p1, p2, p3)
        assert 40 < angle < 50, f"锐角计算错误: {angle}"
        
        print(f"\n角度计算测试:")
        print(f"  直角测试: {angle:.1f}°")
    
    def test_synthetic_corner_detection(self, corrector):
        """测试合成图像的折角检测"""
        # 创建一个简单的测试图像：白色背景上的黑色矩形
        image = np.ones((300, 400, 3), dtype=np.uint8) * 255
        
        # 画一个有缺角的矩形
        cv2.rectangle(image, (50, 50), (350, 250), (0, 0, 0), 2)
        
        # 在角落添加一个三角形缺口（模拟折角）
        triangle_points = np.array([[50, 50], [80, 50], [50, 80]], np.int32)
        cv2.fillPoly(image, [triangle_points], (255, 255, 255))
        
        # 检测轮廓
        contour = corrector._find_document_contour(image)
        
        if contour is not None:
            # 检测折角
            folded_corners = corrector._detect_folded_corners(contour, image)
            
            print(f"\n合成图像测试:")
            print(f"  检测到轮廓点数: {len(contour)}")
            print(f"  检测到折角数: {len(folded_corners)}")
            
            for i, corner in enumerate(folded_corners):
                print(f"  折角 {i+1}: 位置{corner['position']}, 角度{corner['angle']:.1f}°")
        else:
            print("\n合成图像测试: 未检测到轮廓")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
