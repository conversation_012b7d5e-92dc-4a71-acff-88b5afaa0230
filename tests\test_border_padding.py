"""
图像补边功能测试
"""

import pytest
import asyncio
import os
import cv2
import numpy as np
from pathlib import Path

# 将项目根目录添加到Python路径
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.services.border_padding import BorderPadding
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

# 定义测试资源路径
TEST_IMAGE_FOLDER = "testimage"


@pytest.fixture(scope="module")
def event_loop():
    """为pytest-asyncio提供事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="module")
def border_padding():
    """初始化补边处理器"""
    return BorderPadding()


@pytest.fixture(scope="module")
def test_image_path():
    """获取测试图像路径"""
    if not os.path.exists(TEST_IMAGE_FOLDER):
        pytest.skip(f"测试图片文件夹不存在: {TEST_IMAGE_FOLDER}")
    
    # 找到第一个图像文件
    for filename in os.listdir(TEST_IMAGE_FOLDER):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            return os.path.join(TEST_IMAGE_FOLDER, filename)
    
    pytest.skip("测试文件夹中没有找到图像文件")


@pytest.mark.asyncio
async def test_add_border_pixels(border_padding: BorderPadding, test_image_path: str):
    """测试像素单位补边"""
    logger.info("测试像素单位补边")
    
    result = await border_padding.add_border(
        image_path=test_image_path,
        border_size=50,  # 四边各50像素
        unit="pixels",
        color_mode="white"
    )
    
    print(f"\n像素补边结果:")
    print(f"  原始尺寸: {result['original_size']['width']}x{result['original_size']['height']}")
    print(f"  新尺寸: {result['new_size']['width']}x{result['new_size']['height']}")
    print(f"  补边: {result['padding']}")
    print(f"  输出文件: {result['output_file']}")
    
    assert result["success"] is True
    assert result["new_size"]["width"] == result["original_size"]["width"] + 100  # 左右各50
    assert result["new_size"]["height"] == result["original_size"]["height"] + 100  # 上下各50
    assert os.path.exists(result["output_file"])


@pytest.mark.asyncio
async def test_add_border_mm(border_padding: BorderPadding, test_image_path: str):
    """测试毫米单位补边"""
    logger.info("测试毫米单位补边")
    
    result = await border_padding.add_border(
        image_path=test_image_path,
        border_size=10,  # 四边各10毫米
        unit="mm",
        dpi=300,
        color_mode="white"
    )
    
    print(f"\n毫米补边结果:")
    print(f"  原始尺寸: {result['original_size']['width']}x{result['original_size']['height']}")
    print(f"  新尺寸: {result['new_size']['width']}x{result['new_size']['height']}")
    print(f"  补边: {result['padding']}")
    print(f"  DPI: {result['dpi']}")
    print(f"  输出文件: {result['output_file']}")
    
    assert result["success"] is True
    # 10mm在300DPI下约为118像素
    expected_pixels = int(10 / 25.4 * 300)
    assert abs(result["padding"]["top"] - expected_pixels) <= 1
    assert os.path.exists(result["output_file"])


@pytest.mark.asyncio
async def test_target_size(border_padding: BorderPadding, test_image_path: str):
    """测试目标尺寸补边"""
    logger.info("测试目标尺寸补边")
    
    # 读取原始图像获取尺寸
    image = cv2.imread(test_image_path)
    original_height, original_width = image.shape[:2]
    
    target_width = original_width + 200
    target_height = original_height + 150
    
    result = await border_padding.add_border(
        image_path=test_image_path,
        target_size=(target_width, target_height),
        unit="pixels",
        color_mode="white"
    )
    
    print(f"\n目标尺寸补边结果:")
    print(f"  原始尺寸: {result['original_size']['width']}x{result['original_size']['height']}")
    print(f"  目标尺寸: {target_width}x{target_height}")
    print(f"  新尺寸: {result['new_size']['width']}x{result['new_size']['height']}")
    print(f"  补边: {result['padding']}")
    print(f"  输出文件: {result['output_file']}")
    
    assert result["success"] is True
    assert result["new_size"]["width"] == target_width
    assert result["new_size"]["height"] == target_height
    assert os.path.exists(result["output_file"])


@pytest.mark.asyncio
async def test_different_color_modes(border_padding: BorderPadding, test_image_path: str):
    """测试不同颜色模式"""
    logger.info("测试不同颜色模式")
    
    color_modes = ["white", "black", "edge_color", "dominant_color"]
    
    for color_mode in color_modes:
        result = await border_padding.add_border(
            image_path=test_image_path,
            border_size=30,
            unit="pixels",
            color_mode=color_mode
        )
        
        print(f"\n{color_mode} 模式结果:")
        print(f"  颜色: {result['border_color']}")
        print(f"  输出文件: {result['output_file']}")
        
        assert result["success"] is True
        assert "border_color" in result
        assert os.path.exists(result["output_file"])


@pytest.mark.asyncio
async def test_custom_color(border_padding: BorderPadding, test_image_path: str):
    """测试自定义颜色"""
    logger.info("测试自定义颜色")
    
    custom_color = (0, 255, 0)  # 绿色 (BGR格式)
    
    result = await border_padding.add_border(
        image_path=test_image_path,
        border_size=25,
        unit="pixels",
        color_mode="custom",
        custom_color=custom_color
    )
    
    print(f"\n自定义颜色结果:")
    print(f"  设定颜色: {custom_color}")
    print(f"  实际颜色: {result['border_color']}")
    print(f"  输出文件: {result['output_file']}")
    
    assert result["success"] is True
    assert result["border_color"] == list(custom_color)
    assert os.path.exists(result["output_file"])


@pytest.mark.asyncio
async def test_asymmetric_border(border_padding: BorderPadding, test_image_path: str):
    """测试不对称补边"""
    logger.info("测试不对称补边")
    
    # (上, 右, 下, 左)
    border_size = (20, 40, 30, 10)
    
    result = await border_padding.add_border(
        image_path=test_image_path,
        border_size=border_size,
        unit="pixels",
        color_mode="white"
    )
    
    print(f"\n不对称补边结果:")
    print(f"  设定补边: 上{border_size[0]}, 右{border_size[1]}, 下{border_size[2]}, 左{border_size[3]}")
    print(f"  实际补边: {result['padding']}")
    print(f"  输出文件: {result['output_file']}")
    
    assert result["success"] is True
    assert result["padding"]["top"] == border_size[0]
    assert result["padding"]["right"] == border_size[1]
    assert result["padding"]["bottom"] == border_size[2]
    assert result["padding"]["left"] == border_size[3]
    assert os.path.exists(result["output_file"])


@pytest.mark.asyncio
async def test_batch_processing(border_padding: BorderPadding):
    """测试批量处理"""
    logger.info("测试批量处理")
    
    if not os.path.exists(TEST_IMAGE_FOLDER):
        pytest.skip(f"测试文件夹不存在: {TEST_IMAGE_FOLDER}")
    
    result = await border_padding.batch_add_border(
        folder_path=TEST_IMAGE_FOLDER,
        border_size=20,
        unit="pixels",
        color_mode="white"
    )
    
    print(f"\n批量处理结果:")
    print(f"  总文件数: {result['total_files']}")
    print(f"  成功数: {result['success_count']}")
    print(f"  失败数: {result['error_count']}")
    
    assert result["success"] is True
    assert result["total_files"] > 0
    assert result["success_count"] >= 0


if __name__ == "__main__":
    """直接运行补边测试"""
    import asyncio
    
    async def run_border_tests():
        """运行补边测试"""
        border_padding = BorderPadding()
        
        print(f"🚀 开始补边功能测试...")
        print(f"📁 测试文件夹: {TEST_IMAGE_FOLDER}")
        
        if not os.path.exists(TEST_IMAGE_FOLDER):
            print(f"❌ 测试文件夹不存在: {TEST_IMAGE_FOLDER}")
            return
        
        # 找到测试图像
        test_image = None
        for filename in os.listdir(TEST_IMAGE_FOLDER):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                test_image = os.path.join(TEST_IMAGE_FOLDER, filename)
                break
        
        if not test_image:
            print(f"❌ 测试文件夹中没有找到图像文件")
            return
        
        print(f"📷 使用测试图像: {test_image}")
        
        # 运行各种测试
        print(f"\n" + "="*60)
        await test_add_border_pixels(border_padding, test_image)
        
        print(f"\n" + "="*60)
        await test_add_border_mm(border_padding, test_image)
        
        print(f"\n" + "="*60)
        await test_target_size(border_padding, test_image)
        
        print(f"\n" + "="*60)
        await test_different_color_modes(border_padding, test_image)
        
        print(f"\n" + "="*60)
        await test_custom_color(border_padding, test_image)
        
        print(f"\n" + "="*60)
        await test_asymmetric_border(border_padding, test_image)
        
        print(f"\n" + "="*60)
        await test_batch_processing(border_padding)
        
        print(f"\n✅ 补边功能测试完成!")
    
    # 运行测试
    asyncio.run(run_border_tests())
