"""
图像补边模块
支持按像素或毫米补边，智能颜色选择
"""

import cv2
import numpy as np
import os
from typing import Dict, Any, Tuple, Optional, Union
from pathlib import Path
import logging

from app.utils.logger import setup_logger

logger = setup_logger(__name__)


class BorderPadding:
    """图像补边处理器"""
    
    def __init__(self):
        # 常见DPI设置
        self.default_dpi = 300  # 默认DPI
        
        # 支持的图像格式
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
    
    async def add_border(self, image_path: str, 
                        target_size: Optional[Tuple[int, int]] = None,
                        border_size: Optional[Union[int, Tuple[int, int, int, int]]] = None,
                        unit: str = "pixels",
                        dpi: int = 300,
                        color_mode: str = "white",
                        custom_color: Optional[Tuple[int, int, int]] = None,
                        output_path: Optional[str] = None) -> Dict[str, Any]:
        """
        为图像添加边框
        
        Args:
            image_path: 输入图像路径
            target_size: 目标尺寸 (width, height) - 优先级最高
            border_size: 边框大小，可以是:
                - int: 四边相同大小
                - tuple(int, int): (水平, 垂直)
                - tuple(int, int, int, int): (上, 右, 下, 左)
            unit: 单位 ("pixels" 或 "mm")
            dpi: DPI设置 (当unit为mm时使用)
            color_mode: 颜色模式 ("white", "black", "edge_color", "dominant_color", "custom")
            custom_color: 自定义颜色 (B, G, R)
            output_path: 输出路径，如果为None则自动生成
        
        Returns:
            处理结果
        """
        try:
            # 验证输入文件
            if not os.path.exists(image_path):
                return {"error": f"文件不存在: {image_path}"}
            
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                return {"error": f"无法读取图像: {image_path}"}
            
            original_height, original_width = image.shape[:2]
            logger.info(f"原始图像尺寸: {original_width}x{original_height}")
            
            # 计算补边参数
            if target_size:
                # 按目标尺寸计算补边
                target_width, target_height = target_size
                if unit == "mm":
                    target_width = self._mm_to_pixels(target_width, dpi)
                    target_height = self._mm_to_pixels(target_height, dpi)
                
                padding = self._calculate_padding_for_target_size(
                    original_width, original_height, target_width, target_height
                )
            elif border_size:
                # 按指定边框大小补边
                padding = self._parse_border_size(border_size, unit, dpi)
            else:
                return {"error": "必须指定target_size或border_size"}
            
            # 计算补边颜色
            border_color = self._calculate_border_color(image, color_mode, custom_color)
            
            # 应用补边
            padded_image = cv2.copyMakeBorder(
                image,
                padding['top'], padding['bottom'], 
                padding['left'], padding['right'],
                cv2.BORDER_CONSTANT,
                value=border_color
            )
            
            # 生成输出路径
            if output_path is None:
                output_path = self._generate_output_path(image_path, "bordered")
            
            # 保存图像
            success = cv2.imwrite(output_path, padded_image)
            if not success:
                return {"error": f"保存图像失败: {output_path}"}
            
            new_height, new_width = padded_image.shape[:2]
            
            return {
                "success": True,
                "input_file": image_path,
                "output_file": output_path,
                "original_size": {"width": original_width, "height": original_height},
                "new_size": {"width": new_width, "height": new_height},
                "padding": padding,
                "border_color": border_color.tolist() if isinstance(border_color, np.ndarray) else border_color,
                "color_mode": color_mode,
                "unit": unit,
                "dpi": dpi if unit == "mm" else None
            }
            
        except Exception as e:
            logger.error(f"补边处理失败: {str(e)}")
            return {"error": f"补边处理失败: {str(e)}"}
    
    async def batch_add_border(self, folder_path: str, **kwargs) -> Dict[str, Any]:
        """
        批量补边处理
        
        Args:
            folder_path: 文件夹路径
            **kwargs: 传递给add_border的参数
        
        Returns:
            批量处理结果
        """
        try:
            if not os.path.exists(folder_path):
                return {"error": f"文件夹不存在: {folder_path}"}
            
            # 扫描图像文件
            image_files = []
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    file_ext = os.path.splitext(file)[1].lower()
                    if file_ext in self.supported_formats:
                        image_files.append(file_path)
            
            if not image_files:
                return {"error": "文件夹中没有找到支持的图像文件"}
            
            results = []
            success_count = 0
            error_count = 0
            
            logger.info(f"开始批量处理 {len(image_files)} 个文件...")
            
            for image_path in image_files:
                result = await self.add_border(image_path, **kwargs)
                results.append({
                    "file": image_path,
                    "result": result
                })
                
                if result.get("success"):
                    success_count += 1
                else:
                    error_count += 1
                    logger.error(f"处理失败 {image_path}: {result.get('error')}")
            
            return {
                "success": True,
                "total_files": len(image_files),
                "success_count": success_count,
                "error_count": error_count,
                "results": results
            }
            
        except Exception as e:
            logger.error(f"批量补边处理失败: {str(e)}")
            return {"error": f"批量补边处理失败: {str(e)}"}
    
    def _mm_to_pixels(self, mm: float, dpi: int) -> int:
        """毫米转像素"""
        inches = mm / 25.4  # 1英寸 = 25.4毫米
        pixels = int(inches * dpi)
        return pixels
    
    def _pixels_to_mm(self, pixels: int, dpi: int) -> float:
        """像素转毫米"""
        inches = pixels / dpi
        mm = inches * 25.4
        return mm
    
    def _calculate_padding_for_target_size(self, original_width: int, original_height: int,
                                         target_width: int, target_height: int) -> Dict[str, int]:
        """计算达到目标尺寸所需的补边"""
        if target_width < original_width or target_height < original_height:
            raise ValueError("目标尺寸不能小于原始尺寸")
        
        width_diff = target_width - original_width
        height_diff = target_height - original_height
        
        # 居中补边
        left = width_diff // 2
        right = width_diff - left
        top = height_diff // 2
        bottom = height_diff - top
        
        return {
            "top": top,
            "bottom": bottom,
            "left": left,
            "right": right
        }
    
    def _parse_border_size(self, border_size: Union[int, Tuple], unit: str, dpi: int) -> Dict[str, int]:
        """解析边框大小参数"""
        if isinstance(border_size, int):
            # 四边相同
            size = border_size
            if unit == "mm":
                size = self._mm_to_pixels(size, dpi)
            return {"top": size, "bottom": size, "left": size, "right": size}
        
        elif isinstance(border_size, tuple):
            if len(border_size) == 2:
                # (水平, 垂直)
                horizontal, vertical = border_size
                if unit == "mm":
                    horizontal = self._mm_to_pixels(horizontal, dpi)
                    vertical = self._mm_to_pixels(vertical, dpi)
                return {"top": vertical, "bottom": vertical, "left": horizontal, "right": horizontal}
            
            elif len(border_size) == 4:
                # (上, 右, 下, 左)
                top, right, bottom, left = border_size
                if unit == "mm":
                    top = self._mm_to_pixels(top, dpi)
                    right = self._mm_to_pixels(right, dpi)
                    bottom = self._mm_to_pixels(bottom, dpi)
                    left = self._mm_to_pixels(left, dpi)
                return {"top": top, "bottom": bottom, "left": left, "right": right}
        
        raise ValueError("border_size格式错误")
    
    def _calculate_border_color(self, image: np.ndarray, color_mode: str, 
                              custom_color: Optional[Tuple[int, int, int]]) -> Tuple[int, int, int]:
        """计算补边颜色"""
        if color_mode == "white":
            return (255, 255, 255)
        elif color_mode == "black":
            return (0, 0, 0)
        elif color_mode == "custom" and custom_color:
            return custom_color
        elif color_mode == "edge_color":
            return self._get_edge_color(image)
        elif color_mode == "dominant_color":
            return self._get_dominant_color(image)
        else:
            # 默认白色
            return (255, 255, 255)
    
    def _get_edge_color(self, image: np.ndarray) -> Tuple[int, int, int]:
        """获取图像边缘的平均颜色"""
        height, width = image.shape[:2]
        
        # 提取边缘像素
        edge_pixels = []
        
        # 上边缘
        edge_pixels.extend(image[0, :].reshape(-1, 3))
        # 下边缘
        edge_pixels.extend(image[height-1, :].reshape(-1, 3))
        # 左边缘 (排除角落避免重复)
        edge_pixels.extend(image[1:height-1, 0].reshape(-1, 3))
        # 右边缘 (排除角落避免重复)
        edge_pixels.extend(image[1:height-1, width-1].reshape(-1, 3))
        
        edge_pixels = np.array(edge_pixels)
        
        # 计算平均颜色
        avg_color = np.mean(edge_pixels, axis=0)
        return tuple(map(int, avg_color))
    
    def _get_dominant_color(self, image: np.ndarray) -> Tuple[int, int, int]:
        """获取图像的主导颜色"""
        # 缩小图像以提高计算速度
        small_image = cv2.resize(image, (100, 100))
        pixels = small_image.reshape(-1, 3)
        
        # 使用K-means聚类找到主导颜色
        from sklearn.cluster import KMeans
        
        kmeans = KMeans(n_clusters=5, random_state=42, n_init=10)
        kmeans.fit(pixels)
        
        # 找到最大的聚类
        labels = kmeans.labels_
        unique, counts = np.unique(labels, return_counts=True)
        dominant_cluster = unique[np.argmax(counts)]
        
        dominant_color = kmeans.cluster_centers_[dominant_cluster]
        return tuple(map(int, dominant_color))
    
    def _generate_output_path(self, input_path: str, suffix: str) -> str:
        """生成输出文件路径"""
        path = Path(input_path)
        output_name = f"{path.stem}_{suffix}{path.suffix}"
        return str(path.parent / output_name)
