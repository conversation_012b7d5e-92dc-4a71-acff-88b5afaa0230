# 图像补边功能使用指南

## 功能概述

图像补边功能可以为图像添加边框，支持多种尺寸单位、颜色模式和补边方式。

## 主要特性

### 1. 多种尺寸指定方式
- **目标尺寸**: 指定最终图像的目标尺寸
- **边框大小**: 指定要添加的边框大小
  - 四边相同: `50`
  - 水平垂直: `[horizontal, vertical]`
  - 四边不同: `[top, right, bottom, left]`

### 2. 单位支持
- **像素 (pixels)**: 精确的像素级控制
- **毫米 (mm)**: 基于DPI的物理尺寸控制

### 3. 智能颜色选择
- **white**: 纯白色边框
- **black**: 纯黑色边框
- **edge_color**: 基于图像边缘的平均颜色
- **dominant_color**: 基于图像主导颜色
- **custom**: 自定义颜色

## 使用示例

### Python API 使用

```python
from app.services.border_padding import BorderPadding

# 初始化补边处理器
border_padding = BorderPadding()

# 示例1: 添加50像素白边
result = await border_padding.add_border(
    image_path="input.jpg",
    border_size=50,
    unit="pixels",
    color_mode="white"
)

# 示例2: 补边到指定尺寸
result = await border_padding.add_border(
    image_path="input.jpg",
    target_size=(2000, 1500),  # 目标尺寸
    unit="pixels",
    color_mode="edge_color"
)

# 示例3: 毫米单位补边
result = await border_padding.add_border(
    image_path="input.jpg",
    border_size=10,  # 10毫米
    unit="mm",
    dpi=300,
    color_mode="white"
)

# 示例4: 不对称补边
result = await border_padding.add_border(
    image_path="input.jpg",
    border_size=(20, 40, 30, 10),  # 上右下左
    unit="pixels",
    color_mode="dominant_color"
)

# 示例5: 自定义颜色
result = await border_padding.add_border(
    image_path="input.jpg",
    border_size=25,
    unit="pixels",
    color_mode="custom",
    custom_color=(0, 255, 0)  # 绿色 (BGR格式)
)

# 示例6: 批量处理
result = await border_padding.batch_add_border(
    folder_path="images/",
    border_size=30,
    unit="pixels",
    color_mode="white"
)
```

### REST API 使用

#### 1. 单张图像补边

```bash
curl -X POST "http://localhost:8000/api/border-padding/add-border" \
  -F "file=@image.jpg" \
  -F "border_size=50" \
  -F "unit=pixels" \
  -F "color_mode=white"
```

#### 2. 目标尺寸补边

```bash
curl -X POST "http://localhost:8000/api/border-padding/add-border" \
  -F "file=@image.jpg" \
  -F "target_size=[2000, 1500]" \
  -F "unit=pixels" \
  -F "color_mode=edge_color"
```

#### 3. 毫米单位补边

```bash
curl -X POST "http://localhost:8000/api/border-padding/add-border" \
  -F "file=@image.jpg" \
  -F "border_size=10" \
  -F "unit=mm" \
  -F "dpi=300" \
  -F "color_mode=white"
```

#### 4. 自定义颜色补边

```bash
curl -X POST "http://localhost:8000/api/border-padding/add-border" \
  -F "file=@image.jpg" \
  -F "border_size=25" \
  -F "unit=pixels" \
  -F "color_mode=custom" \
  -F "custom_color=[255, 0, 0]"
```

#### 5. 批量处理

```bash
curl -X POST "http://localhost:8000/api/border-padding/batch-add-border" \
  -F "folder_path=/path/to/images" \
  -F "border_size=30" \
  -F "unit=pixels" \
  -F "color_mode=white"
```

#### 6. 颜色预览

```bash
curl -X POST "http://localhost:8000/api/border-padding/preview-border-color" \
  -F "file=@image.jpg" \
  -F "color_mode=edge_color"
```

## 参数说明

### border_size 格式
- **整数**: `50` - 四边相同大小
- **数组(2个元素)**: `[30, 20]` - [水平, 垂直]
- **数组(4个元素)**: `[10, 20, 15, 25]` - [上, 右, 下, 左]

### color_mode 选项
- `white`: 纯白色 (255, 255, 255)
- `black`: 纯黑色 (0, 0, 0)
- `edge_color`: 图像边缘平均颜色
- `dominant_color`: 图像主导颜色 (使用K-means聚类)
- `custom`: 自定义颜色 (需要提供custom_color)

### 单位转换
- **像素到毫米**: `mm = pixels / dpi * 25.4`
- **毫米到像素**: `pixels = mm / 25.4 * dpi`

## 返回结果

```json
{
  "success": true,
  "input_file": "input.jpg",
  "output_file": "input_bordered.jpg",
  "original_size": {"width": 1000, "height": 800},
  "new_size": {"width": 1100, "height": 900},
  "padding": {
    "top": 50,
    "bottom": 50,
    "left": 50,
    "right": 50
  },
  "border_color": [255, 255, 255],
  "color_mode": "white",
  "unit": "pixels"
}
```

## 注意事项

1. **文件格式**: 支持 JPG, PNG, BMP, TIFF 等常见格式
2. **DPI设置**: 毫米单位时需要正确设置DPI值
3. **目标尺寸**: 目标尺寸不能小于原始尺寸
4. **颜色格式**: 
   - Python API使用BGR格式 (OpenCV标准)
   - REST API返回RGB格式便于前端使用
5. **内存使用**: 大图像处理时注意内存占用
6. **文件命名**: 输出文件自动添加"_bordered"后缀

## 常见用例

### 1. 证件照补边
```python
# 补边到标准证件照尺寸
result = await border_padding.add_border(
    image_path="photo.jpg",
    target_size=(413, 531),  # 1寸照片像素尺寸
    unit="pixels",
    color_mode="white"
)
```

### 2. 打印补边
```python
# 补边到A4纸张尺寸
result = await border_padding.add_border(
    image_path="document.jpg",
    target_size=(210, 297),  # A4尺寸(mm)
    unit="mm",
    dpi=300,
    color_mode="white"
)
```

### 3. 艺术效果
```python
# 使用图像主导颜色创建艺术边框
result = await border_padding.add_border(
    image_path="artwork.jpg",
    border_size=100,
    unit="pixels",
    color_mode="dominant_color"
)
```
