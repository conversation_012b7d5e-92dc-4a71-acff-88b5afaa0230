"""
折角检测和修复模块
检测并修复扫描文档中的折角问题
"""

import os
import cv2
import numpy as np
from typing import Dict, Any, List, Union, Tuple
from pathlib import Path

from app.utils.logger import setup_logger
from app.utils.image_utils import ImageUtils

logger = setup_logger(__name__)


class CornerCorrector:
    """折角检测和修复器"""

    def __init__(self):
        self.image_utils = ImageUtils()

        # 检测参数
        self.edge_threshold_low = 50
        self.edge_threshold_high = 150
        self.contour_area_threshold = 1000  # 最小轮廓面积
        self.corner_angle_threshold = 45    # 折角角度阈值（度）- 更严格
        self.min_fold_size = 20            # 最小折角尺寸
        self.min_shadow_contrast = 30      # 最小阴影对比度
        self.shadow_search_radius = 100    # 阴影搜索半径

        # 支持的图像格式
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}

    async def detect(self, input_data: Union[str, np.ndarray], options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        检测折角（不修改文件）
        
        Args:
            input_data: 输入数据，可以是文件路径、文件夹路径或图像数组
            options: 检测选项
            
        Returns:
            检测结果字典
        """
        if options is None:
            options = {}

        logger.info("开始折角检测")

        if isinstance(input_data, np.ndarray):
            # 直接处理图像数组
            return await self._detect_single_image(input_data, "image_array", options)

        elif isinstance(input_data, str):
            path = Path(input_data)
            
            if path.is_file():
                return await self._detect_single_file(str(path), options)
            elif path.is_dir():
                return await self._detect_folder(str(path), options)
            else:
                raise FileNotFoundError(f"路径不存在: {input_data}")

        else:
            raise ValueError(f"不支持的输入类型: {type(input_data)}")

    async def _detect_single_image(self, image: np.ndarray, image_name: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """检测单张图像的折角"""
        try:
            # 设置当前图像名称，用于生成友好的调试文件名
            self._current_image_name = image_name

            # 预处理图像
            processed_image = self._preprocess_for_detection(image)
            
            # 检测文档轮廓
            document_contour = self._find_document_contour(processed_image)
            
            if document_contour is None:
                logger.warning(f"图像 {image_name}: 未找到文档轮廓")
                return {
                    "image_name": image_name,
                    "has_folded_corners": False,
                    "folded_corners": [],
                    "confidence": 0.0,
                    "needs_correction": False
                }
            
            # 检测折角
            folded_corners = self._detect_folded_corners(document_contour, processed_image)
            
            has_folded_corners = len(folded_corners) > 0
            confidence = self._calculate_confidence(folded_corners, document_contour)
            needs_correction = has_folded_corners and confidence > 0.2
            
            logger.info(f"图像 {image_name}: 检测到{len(folded_corners)}个折角, 置信度={confidence:.3f}, 需要修复={needs_correction}")
            
            return {
                "image_name": image_name,
                "has_folded_corners": has_folded_corners,
                "folded_corners": folded_corners,
                "confidence": confidence,
                "needs_correction": needs_correction,
                "document_contour": document_contour.tolist() if document_contour is not None else None
            }

        except Exception as e:
            logger.error(f"检测图像 {image_name} 折角失败: {str(e)}")
            return {
                "image_name": image_name,
                "has_folded_corners": False,
                "folded_corners": [],
                "confidence": 0.0,
                "needs_correction": False,
                "error": str(e)
            }

    def _preprocess_for_detection(self, image: np.ndarray) -> np.ndarray:
        """
        为检测预处理图像
        
        Args:
            image: 输入图像
            
        Returns:
            预处理后的图像
        """
        # 缩放图像以提高检测速度
        height, width = image.shape[:2]
        max_dim = max(height, width)
        max_size = 1024
        
        if max_dim > max_size:
            ratio = max_size / max_dim
            new_width = int(width * ratio)
            new_height = int(height * ratio)
            processed_image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
        else:
            processed_image = image.copy()
        
        return processed_image

    def _find_document_contour(self, image: np.ndarray) -> np.ndarray:
        """
        找到文档的主要轮廓
        
        Args:
            image: 输入图像
            
        Returns:
            文档轮廓点数组，如果未找到则返回None
        """
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # 边缘检测
        edges = cv2.Canny(gray, self.edge_threshold_low, self.edge_threshold_high)
        
        # 形态学操作，连接断开的边缘
        kernel = np.ones((3, 3), np.uint8)
        edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
        
        # 找到轮廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return None
        
        # 找到最大的轮廓（假设是文档）
        largest_contour = max(contours, key=cv2.contourArea)
        
        # 检查轮廓面积是否足够大
        if cv2.contourArea(largest_contour) < self.contour_area_threshold:
            return None
        
        # 轮廓近似，减少点数
        epsilon = 0.02 * cv2.arcLength(largest_contour, True)
        approx_contour = cv2.approxPolyDP(largest_contour, epsilon, True)
        
        return approx_contour

    def _detect_folded_corners(self, contour: np.ndarray, image: np.ndarray) -> List[Dict[str, Any]]:
        """
        检测折角（基于矩形切割 + 角落完整性检测）

        Args:
            contour: 文档轮廓
            image: 原始图像

        Returns:
            折角信息列表
        """
        folded_corners = []

        try:
            # 1. 简单按百分比裁切四周（去除扫描仪多扫的部分）
            cropped_doc = self._crop_by_percentage(image, crop_percent=0.02)  # 裁掉2%

            # 2. 二值化处理，去除颜色干扰
            binary_doc = self._binarize_image(cropped_doc)

            # 保存处理后的图像用于检查
            import os
            if hasattr(self, '_current_image_name'):
                base_name = os.path.splitext(self._current_image_name)[0]
            else:
                base_name = "unknown"
            self._save_cropped_image(cropped_doc, f"{base_name}_step1_cropped")
            self._save_cropped_image(binary_doc, f"{base_name}_step2_binary")

            # 3. 检查四个角落的完整性（使用二值化图像）
            corners_to_check = ["top_left", "top_right", "bottom_left", "bottom_right"]

            for corner_name in corners_to_check:
                is_folded, integrity_score = self._check_corner_integrity(binary_doc, corner_name)

                if is_folded:
                    # 计算在裁切后图像中的位置（简化）
                    height, width = cropped_doc.shape[:2]
                    corner_pos = self._get_corner_position_in_cropped(width, height, corner_name)

                    folded_corners.append({
                        "position": corner_pos,
                        "corner_name": corner_name,
                        "corner_type": "folded_corner",
                        "integrity_score": integrity_score
                    })

            return folded_corners

        except Exception as e:
            print(f"折角检测失败: {e}")
            return []

    def _crop_by_percentage(self, image: np.ndarray, crop_percent: float = 0.02) -> np.ndarray:
        """
        按百分比裁切图像四周（去除扫描仪多扫的部分）

        Args:
            image: 原始图像
            crop_percent: 裁切百分比，默认2%

        Returns:
            裁切后的图像
        """
        height, width = image.shape[:2]

        # 计算裁切的像素数
        crop_top = int(height * crop_percent)
        crop_bottom = int(height * (1 - crop_percent))
        crop_left = int(width * crop_percent)
        crop_right = int(width * (1 - crop_percent))

        # 裁切图像
        cropped = image[crop_top:crop_bottom, crop_left:crop_right]

        print(f"原图尺寸: {width}x{height}, 裁切后: {cropped.shape[1]}x{cropped.shape[0]} (裁切{crop_percent*100:.1f}%)")

        return cropped

    def _binarize_image(self, image: np.ndarray) -> np.ndarray:
        """
        二值化图像，去除颜色干扰

        Args:
            image: 输入图像

        Returns:
            二值化后的图像
        """
        # 转换为灰度
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # 使用自适应阈值二值化，能更好地处理光照不均
        binary = cv2.adaptiveThreshold(
            gray,
            255,
            cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
            cv2.THRESH_BINARY,
            11,  # 邻域大小
            2    # 常数C
        )

        # 转换回3通道，保持与原图像格式一致
        binary_3ch = cv2.cvtColor(binary, cv2.COLOR_GRAY2BGR)

        print(f"二值化完成，阈值方法: 自适应高斯")

        return binary_3ch

    def _check_white_triangle(self, corner_region: np.ndarray, corner_name: str) -> int:
        """
        检查角落区域是否有白色三角形（折角的主要特征）

        Args:
            corner_region: 角落区域的二值化图像
            corner_name: 角落名称

        Returns:
            白色三角形得分 (0-2)
        """
        try:
            # 转换为灰度（如果是3通道）
            if len(corner_region.shape) == 3:
                gray = cv2.cvtColor(corner_region, cv2.COLOR_BGR2GRAY)
            else:
                gray = corner_region.copy()

            h, w = gray.shape

            # 定义角落的期望区域（三角形）
            if corner_name == "top_left":
                # 左上角：检查左上三角形区域
                mask = np.zeros_like(gray)
                points = np.array([[0, 0], [w//3, 0], [0, h//3]], np.int32)
                cv2.fillPoly(mask, [points], 255)
            elif corner_name == "top_right":
                # 右上角：检查右上三角形区域
                mask = np.zeros_like(gray)
                points = np.array([[w, 0], [w-w//3, 0], [w, h//3]], np.int32)
                cv2.fillPoly(mask, [points], 255)
            elif corner_name == "bottom_left":
                # 左下角：检查左下三角形区域
                mask = np.zeros_like(gray)
                points = np.array([[0, h], [w//3, h], [0, h-h//3]], np.int32)
                cv2.fillPoly(mask, [points], 255)
            elif corner_name == "bottom_right":
                # 右下角：检查右下三角形区域
                mask = np.zeros_like(gray)
                points = np.array([[w, h], [w-w//3, h], [w, h-h//3]], np.int32)
                cv2.fillPoly(mask, [points], 255)
            else:
                return 0

            # 计算三角形区域内的白色像素比例
            triangle_area = cv2.bitwise_and(gray, mask)
            white_pixels = np.sum(triangle_area > 200)  # 白色像素
            total_pixels = np.sum(mask > 0)

            if total_pixels == 0:
                return 0

            white_ratio = white_pixels / total_pixels

            # 根据白色比例给分
            if white_ratio > 0.7:  # 70%以上是白色，很可能是折角
                return 2
            elif white_ratio > 0.4:  # 40%以上是白色，可能是折角
                return 1
            else:
                return 0

        except Exception:
            return 0

    def _check_boundary_continuity(self, corner_region: np.ndarray, corner_name: str) -> int:
        """
        检查角落边界的连续性

        Args:
            corner_region: 角落区域的二值化图像
            corner_name: 角落名称

        Returns:
            边界连续性得分 (0-1)
        """
        try:
            # 转换为灰度
            if len(corner_region.shape) == 3:
                gray = cv2.cvtColor(corner_region, cv2.COLOR_BGR2GRAY)
            else:
                gray = corner_region.copy()

            h, w = gray.shape

            # 检查角落的两条边界线
            if corner_name == "top_left":
                # 检查左边界和上边界
                left_edge = gray[:, 0:3]  # 左边3列
                top_edge = gray[0:3, :]   # 上边3行
            elif corner_name == "top_right":
                # 检查右边界和上边界
                left_edge = gray[:, w-3:w]  # 右边3列
                top_edge = gray[0:3, :]     # 上边3行
            elif corner_name == "bottom_left":
                # 检查左边界和下边界
                left_edge = gray[:, 0:3]      # 左边3列
                top_edge = gray[h-3:h, :]     # 下边3行
            elif corner_name == "bottom_right":
                # 检查右边界和下边界
                left_edge = gray[:, w-3:w]    # 右边3列
                top_edge = gray[h-3:h, :]     # 下边3行
            else:
                return 0

            # 计算边界的黑色像素比例（文档边界应该是黑色）
            left_black_ratio = np.sum(left_edge < 100) / left_edge.size
            top_black_ratio = np.sum(top_edge < 100) / top_edge.size

            # 如果边界大部分是白色，说明边界不连续（可能是折角）
            if left_black_ratio < 0.3 or top_black_ratio < 0.3:
                return 1
            else:
                return 0

        except Exception:
            return 0

    def _check_corner_completeness(self, corner_region: np.ndarray, corner_name: str) -> int:
        """
        检查角落的完整性

        Args:
            corner_region: 角落区域的二值化图像
            corner_name: 角落名称

        Returns:
            完整性得分 (0-1)
        """
        try:
            # 转换为灰度
            if len(corner_region.shape) == 3:
                gray = cv2.cvtColor(corner_region, cv2.COLOR_BGR2GRAY)
            else:
                gray = corner_region.copy()

            # 计算整个角落区域的白色像素比例
            white_pixels = np.sum(gray > 200)
            total_pixels = gray.size
            white_ratio = white_pixels / total_pixels

            # 如果白色像素过多，说明角落不完整
            if white_ratio > 0.3:  # 30%以上是白色
                return 1
            else:
                return 0

        except Exception:
            return 0

    def _get_corner_position_in_cropped(self, width: int, height: int, corner_name: str) -> list:
        """
        获取角落在裁切后图像中的位置

        Args:
            width, height: 裁切后图像的尺寸
            corner_name: 角落名称

        Returns:
            [x, y] 坐标
        """
        if corner_name == "top_left":
            return [0, 0]
        elif corner_name == "top_right":
            return [width, 0]
        elif corner_name == "bottom_left":
            return [0, height]
        elif corner_name == "bottom_right":
            return [width, height]
        else:
            return [0, 0]

    def _find_document_rectangle(self, contour: np.ndarray, image: np.ndarray) -> tuple:
        """
        找到文档的矩形边界（智能检测，排除印章等干扰）

        Args:
            contour: 文档轮廓
            image: 原始图像

        Returns:
            (x, y, width, height) 或 None
        """
        try:
            # 方法1: 尝试找到最大的矩形轮廓
            document_rect = self._find_largest_rectangle_contour(image)
            if document_rect is not None:
                return document_rect

            # 方法2: 如果方法1失败，使用改进的边界检测
            return self._find_document_boundary_improved(contour, image)

        except Exception:
            return None

    def _find_largest_rectangle_contour(self, image: np.ndarray) -> tuple:
        """
        找到图像中最大的矩形轮廓（文档主体）

        Args:
            image: 原始图像

        Returns:
            (x, y, width, height) 或 None
        """
        try:
            # 转换为灰度
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # 边缘检测
            edges = cv2.Canny(gray, 50, 150, apertureSize=3)

            # 查找所有轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                return None

            # 找到最大的轮廓
            largest_contour = max(contours, key=cv2.contourArea)

            # 检查是否足够大（应该是文档主体）
            image_area = image.shape[0] * image.shape[1]
            contour_area = cv2.contourArea(largest_contour)

            if contour_area < image_area * 0.1:  # 太小，可能不是文档
                return None

            # 获取外接矩形
            x, y, w, h = cv2.boundingRect(largest_contour)

            # 添加适当边距
            margin = 20
            height, width = image.shape[:2]

            x = max(0, x - margin)
            y = max(0, y - margin)
            w = min(width - x, w + 2 * margin)
            h = min(height - y, h + 2 * margin)

            return (x, y, w, h)

        except Exception:
            return None

    def _find_document_boundary_improved(self, contour: np.ndarray, image: np.ndarray) -> tuple:
        """
        改进的文档边界检测（回退方法）

        Args:
            contour: 文档轮廓
            image: 原始图像

        Returns:
            (x, y, width, height)
        """
        # 使用原始方法作为回退
        x, y, w, h = cv2.boundingRect(contour)

        # 但是缩小边距，避免包含过多干扰元素
        margin = 5
        height, width = image.shape[:2]

        x = max(0, x - margin)
        y = max(0, y - margin)
        w = min(width - x, w + 2 * margin)
        h = min(height - y, h + 2 * margin)

        return (x, y, w, h)

    def _crop_to_rectangle(self, image: np.ndarray, rect: tuple) -> np.ndarray:
        """
        按矩形切割图像

        Args:
            image: 原始图像
            rect: (x, y, width, height)

        Returns:
            切割后的图像 或 None
        """
        try:
            x, y, w, h = rect
            cropped = image[y:y+h, x:x+w]
            return cropped
        except Exception:
            return None

    def _check_corner_integrity(self, binary_doc: np.ndarray, corner_name: str) -> tuple:
        """
        检查角落的完整性（基于二值化图像）

        Args:
            binary_doc: 二值化后的文档图像
            corner_name: 角落名称

        Returns:
            (是否折叠, 完整性分数)
        """
        try:
            height, width = binary_doc.shape[:2]

            # 定义角落区域大小（相对于文档大小）
            corner_size = min(width, height) // 10  # 文档的1/10大小
            corner_size = max(40, min(corner_size, 120))  # 限制在40-120像素

            # 提取角落区域
            corner_region = self._extract_corner_region(binary_doc, corner_name, corner_size)
            if corner_region is None:
                return False, 0.0

            # 基于二值化图像的折角检测
            folded_score = 0

            # 1. 检查白色三角形区域（折角的主要特征）
            white_triangle_score = self._check_white_triangle(corner_region, corner_name)
            folded_score += white_triangle_score

            # 2. 检查边界连续性
            boundary_score = self._check_boundary_continuity(corner_region, corner_name)
            folded_score += boundary_score

            # 3. 检查角落完整性
            completeness_score = self._check_corner_completeness(corner_region, corner_name)
            folded_score += completeness_score

            # 判断是否为折角（提高阈值）
            is_folded = folded_score >= 3  # 提高阈值，减少误检
            integrity_score = min(folded_score / 3.0, 1.0)  # 归一化到0-1

            return is_folded, integrity_score

        except Exception:
            return False, 0.0

    def _extract_corner_region(self, image: np.ndarray, corner_name: str, corner_size: int) -> np.ndarray:
        """提取角落区域"""
        try:
            height, width = image.shape[:2]

            if corner_name == "top_left":
                return image[0:corner_size, 0:corner_size]
            elif corner_name == "top_right":
                return image[0:corner_size, width-corner_size:width]
            elif corner_name == "bottom_left":
                return image[height-corner_size:height, 0:corner_size]
            elif corner_name == "bottom_right":
                return image[height-corner_size:height, width-corner_size:width]
            else:
                return None
        except Exception:
            return None

    def _check_color_continuity(self, corner_region: np.ndarray, corner_name: str) -> bool:
        """
        检查角落区域的颜色连续性

        Args:
            corner_region: 角落区域图像
            corner_name: 角落名称

        Returns:
            是否有颜色连续性问题
        """
        try:
            # 转换为灰度
            gray = cv2.cvtColor(corner_region, cv2.COLOR_BGR2GRAY)

            # 计算角落区域的颜色变化
            # 使用梯度检测颜色变化的剧烈程度
            grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
            gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)

            # 计算梯度的平均值
            avg_gradient = np.mean(gradient_magnitude)

            # 如果梯度过大，说明颜色变化剧烈，可能有问题
            return avg_gradient > 30

        except Exception:
            return False

    def _check_color_anomaly(self, corner_region: np.ndarray) -> bool:
        """
        检查是否有大块异常颜色区域

        Args:
            corner_region: 角落区域图像

        Returns:
            是否有异常颜色
        """
        try:
            # 转换为灰度
            gray = cv2.cvtColor(corner_region, cv2.COLOR_BGR2GRAY)

            # 计算亮度统计
            mean_brightness = np.mean(gray)
            std_brightness = np.std(gray)

            # 检查异常情况
            # 1. 过度曝光（全白）
            if mean_brightness > 240 and std_brightness < 10:
                return True

            # 2. 过度黑暗
            if mean_brightness < 50:
                return True

            # 3. 亮度变化过大
            if std_brightness > 80:
                return True

            return False

        except Exception:
            return False

    def _check_edge_integrity(self, corner_region: np.ndarray, corner_name: str) -> bool:
        """
        检查角落边缘的完整性

        Args:
            corner_region: 角落区域图像
            corner_name: 角落名称

        Returns:
            是否有边缘完整性问题
        """
        try:
            # 转换为灰度
            gray = cv2.cvtColor(corner_region, cv2.COLOR_BGR2GRAY)

            # 边缘检测
            edges = cv2.Canny(gray, 50, 150)

            # 计算边缘密度
            edge_density = np.sum(edges > 0) / edges.size

            # 如果边缘密度过高，可能是折痕或破损
            if edge_density > 0.1:
                return True

            # 检查角落的特定边缘
            h, w = gray.shape

            if corner_name == "top_left":
                # 检查左边缘和上边缘
                left_edge = edges[:, 0:5]
                top_edge = edges[0:5, :]
                edge_issue = np.sum(left_edge) + np.sum(top_edge)
            elif corner_name == "top_right":
                # 检查右边缘和上边缘
                right_edge = edges[:, w-5:w]
                top_edge = edges[0:5, :]
                edge_issue = np.sum(right_edge) + np.sum(top_edge)
            elif corner_name == "bottom_left":
                # 检查左边缘和下边缘
                left_edge = edges[:, 0:5]
                bottom_edge = edges[h-5:h, :]
                edge_issue = np.sum(left_edge) + np.sum(bottom_edge)
            elif corner_name == "bottom_right":
                # 检查右边缘和下边缘
                right_edge = edges[:, w-5:w]
                bottom_edge = edges[h-5:h, :]
                edge_issue = np.sum(right_edge) + np.sum(bottom_edge)
            else:
                edge_issue = 0

            # 如果边缘有太多断裂，可能是折角
            return edge_issue > 100

        except Exception:
            return False

    def _get_corner_position_in_original(self, document_rect: tuple, corner_name: str) -> list:
        """
        获取角落在原图中的位置

        Args:
            document_rect: 文档矩形 (x, y, w, h)
            corner_name: 角落名称

        Returns:
            [x, y] 在原图中的坐标
        """
        x, y, w, h = document_rect

        if corner_name == "top_left":
            return [x, y]
        elif corner_name == "top_right":
            return [x + w, y]
        elif corner_name == "bottom_left":
            return [x, y + h]
        elif corner_name == "bottom_right":
            return [x + w, y + h]
        else:
            return [x, y]

    def _save_cropped_image(self, cropped_doc: np.ndarray, prefix: str):
        """
        保存裁切后的图像用于调试

        Args:
            cropped_doc: 裁切后的文档图像
            prefix: 文件名前缀
        """
        try:
            import time
            timestamp = int(time.time())
            filename = f"{prefix}_{timestamp}.jpg"
            cv2.imwrite(filename, cropped_doc)
            print(f"裁切图像已保存: {filename}")
        except Exception as e:
            print(f"保存裁切图像失败: {e}")

    def _check_corner_missing_by_contour(self, contour: np.ndarray, ideal_point: tuple,
                                        corner_name: str, image_shape: tuple) -> tuple:
        """
        基于轮廓检查角落是否缺失

        Args:
            contour: 文档轮廓
            ideal_point: 理想角点坐标 (x, y)
            corner_name: 角落名称
            image_shape: 图像形状 (height, width)

        Returns:
            (是否缺失, 缺失信息字典)
        """
        ideal_x, ideal_y = ideal_point
        height, width = image_shape

        # 定义检查区域大小（相对于理想角点）
        check_radius = 50

        # 确保检查区域在图像范围内
        min_x = max(0, ideal_x - check_radius)
        max_x = min(width, ideal_x + check_radius)
        min_y = max(0, ideal_y - check_radius)
        max_y = min(height, ideal_y + check_radius)

        # 计算理想角点到轮廓的最短距离
        distances = []
        for point in contour:
            px, py = point[0]
            dist = np.sqrt((px - ideal_x)**2 + (py - ideal_y)**2)
            distances.append(dist)

        min_distance = min(distances) if distances else float('inf')

        # 检查轮廓在角落区域的覆盖情况
        corner_coverage = self._calculate_corner_coverage(contour, ideal_point, corner_name, check_radius)

        # 判断角落是否缺失的条件
        is_missing = False
        missing_area = 0

        # 条件1: 理想角点距离轮廓太远（提高阈值）
        if min_distance > 20:
            is_missing = True
            missing_area += min_distance * 2

        # 条件2: 角落区域覆盖率太低（提高阈值）
        if corner_coverage < 0.3:
            is_missing = True
            missing_area += (1 - corner_coverage) * 100

        missing_info = {
            "distance": min_distance,
            "coverage": corner_coverage,
            "missing_area": missing_area
        }

        return is_missing, missing_info

    def _calculate_corner_coverage(self, contour: np.ndarray, ideal_point: tuple,
                                  corner_name: str, radius: int) -> float:
        """
        计算轮廓在角落区域的覆盖率

        Args:
            contour: 文档轮廓
            ideal_point: 理想角点
            corner_name: 角落名称
            radius: 检查半径

        Returns:
            覆盖率 (0.0 - 1.0)
        """
        ideal_x, ideal_y = ideal_point

        # 创建角落区域掩码
        mask_size = radius * 2
        corner_mask = np.zeros((mask_size, mask_size), dtype=np.uint8)

        # 根据角落类型填充期望的区域
        if corner_name == "top_left":
            corner_mask[radius:, radius:] = 255  # 右下象限
        elif corner_name == "top_right":
            corner_mask[radius:, :radius] = 255  # 左下象限
        elif corner_name == "bottom_left":
            corner_mask[:radius, radius:] = 255  # 右上象限
        elif corner_name == "bottom_right":
            corner_mask[:radius, :radius] = 255  # 左上象限

        # 创建轮廓掩码
        contour_mask = np.zeros((mask_size, mask_size), dtype=np.uint8)

        # 将轮廓转换到局部坐标系
        local_contour = contour.copy()
        local_contour[:, 0, 0] -= (ideal_x - radius)
        local_contour[:, 0, 1] -= (ideal_y - radius)

        # 过滤掉超出范围的点
        valid_points = []
        for point in local_contour:
            x, y = point[0]
            if 0 <= x < mask_size and 0 <= y < mask_size:
                valid_points.append([[x, y]])

        if valid_points:
            valid_contour = np.array(valid_points)
            cv2.fillPoly(contour_mask, [valid_contour], 255)

        # 计算覆盖率
        expected_area = np.sum(corner_mask > 0)
        covered_area = np.sum(cv2.bitwise_and(corner_mask, contour_mask) > 0)

        coverage = covered_area / expected_area if expected_area > 0 else 0.0

        return coverage

    def _analyze_corner_features(self, image: np.ndarray, x: int, y: int, size: int, corner_name: str) -> Dict[str, float]:
        """
        分析角落区域的特征

        Args:
            image: 原始图像
            x, y: 角落区域左上角坐标
            size: 角落区域大小
            corner_name: 角落名称

        Returns:
            特征字典
        """
        # 提取角落区域
        corner_region = image[y:y+size, x:x+size]

        # 转换为灰度
        gray_corner = cv2.cvtColor(corner_region, cv2.COLOR_BGR2GRAY)

        # 特征1: 边缘检测
        edges = cv2.Canny(gray_corner, 50, 150)
        edge_density = np.sum(edges > 0) / (size * size)

        # 特征2: 亮度统计
        mean_brightness = np.mean(gray_corner)
        std_brightness = np.std(gray_corner)

        # 特征3: 角落完整性检测
        corner_mask = self._create_corner_mask_for_analysis(size, corner_name)
        corner_content = cv2.bitwise_and(gray_corner, corner_mask)
        content_ratio = np.sum(corner_content > 50) / np.sum(corner_mask > 0)

        # 特征4: 轮廓分析
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        contour_count = len(contours)
        max_contour_area = max([cv2.contourArea(c) for c in contours]) if contours else 0

        # 特征5: 角点检测
        corners_detected = cv2.goodFeaturesToTrack(gray_corner, maxCorners=10, qualityLevel=0.01, minDistance=10)
        corner_points_count = len(corners_detected) if corners_detected is not None else 0

        return {
            "edge_density": edge_density,
            "mean_brightness": mean_brightness,
            "std_brightness": std_brightness,
            "content_ratio": content_ratio,
            "contour_count": contour_count,
            "max_contour_area": max_contour_area,
            "corner_points": corner_points_count
        }

    def _create_corner_mask_for_analysis(self, size: int, corner_name: str) -> np.ndarray:
        """创建角落分析用的掩码"""
        mask = np.zeros((size, size), dtype=np.uint8)

        if corner_name == "top_left":
            for i in range(size):
                for j in range(size):
                    if i + j >= size // 2:
                        mask[i, j] = 255
        elif corner_name == "top_right":
            for i in range(size):
                for j in range(size):
                    if i + (size - j) >= size // 2:
                        mask[i, j] = 255
        elif corner_name == "bottom_left":
            for i in range(size):
                for j in range(size):
                    if (size - i) + j >= size // 2:
                        mask[i, j] = 255
        elif corner_name == "bottom_right":
            for i in range(size):
                for j in range(size):
                    if (size - i) + (size - j) >= size // 2:
                        mask[i, j] = 255

        return mask

    def _is_corner_folded(self, features: Dict[str, float]) -> tuple:
        """
        基于特征判断角落是否折叠（重新设计，专注于真正的折角特征）

        Args:
            features: 角落特征

        Returns:
            (是否折叠, 异常分数)
        """
        anomaly_score = 0

        # 重新设计的折角检测逻辑

        # 1. 完全白色区域（过度曝光/缺失）- 强烈的折角指示
        if features["mean_brightness"] > 250 and features["corner_points"] == 0:
            anomaly_score += 5

        # 2. 边缘密度极高且轮廓数量很多（复杂折痕）
        if features["edge_density"] > 0.15 and features["contour_count"] > 200:
            anomaly_score += 4

        # 3. 内容比例明显下降（角落缺失）
        if features["content_ratio"] < 0.9:
            anomaly_score += 3

        # 4. 中等边缘密度异常（可能的折痕）
        if 0.05 < features["edge_density"] < 0.15 and features["contour_count"] > 50:
            anomaly_score += 2

        # 5. 亮度过暗（阴影区域）
        if features["mean_brightness"] < 150:
            anomaly_score += 2

        # 6. 轻微的内容不完整
        if 0.9 <= features["content_ratio"] < 0.95:
            anomaly_score += 1

        # 调整后的阈值：需要更明确的折角证据
        is_folded = anomaly_score >= 2

        return is_folded, anomaly_score

    def _find_ideal_document_rect(self, contour: np.ndarray, image: np.ndarray) -> tuple:
        """
        找到文档的理想矩形边界

        Args:
            contour: 文档轮廓
            image: 原始图像

        Returns:
            (x, y, width, height) 或 None
        """
        # 计算轮廓的外接矩形
        x, y, w, h = cv2.boundingRect(contour)

        # 扩展一点边界，确保包含可能的折角区域
        margin = 20
        x = max(0, x - margin)
        y = max(0, y - margin)
        w = min(image.shape[1] - x, w + 2 * margin)
        h = min(image.shape[0] - y, h + 2 * margin)

        return (x, y, w, h)

    def _is_corner_missing(self, contour: np.ndarray, corner_x: int, corner_y: int, corner_name: str) -> bool:
        """
        检查指定角落是否缺失

        Args:
            contour: 文档轮廓
            corner_x, corner_y: 角落坐标
            corner_name: 角落名称

        Returns:
            是否缺失
        """
        # 定义角落检查区域
        check_radius = 50

        # 创建角落区域的掩码
        mask = np.zeros((check_radius * 2, check_radius * 2), dtype=np.uint8)

        # 将轮廓转换到局部坐标系
        local_contour = contour.copy()
        local_contour[:, 0, 0] -= (corner_x - check_radius)
        local_contour[:, 0, 1] -= (corner_y - check_radius)

        # 在掩码上绘制轮廓
        cv2.fillPoly(mask, [local_contour], 255)

        # 检查角落区域的填充情况
        corner_region = self._get_corner_region_mask(check_radius, corner_name)

        # 计算角落区域的覆盖率
        overlap = cv2.bitwise_and(mask, corner_region)
        coverage = np.sum(overlap > 0) / np.sum(corner_region > 0)

        # 如果覆盖率低于阈值，认为角落缺失
        return coverage < 0.7

    def _get_corner_region_mask(self, radius: int, corner_name: str) -> np.ndarray:
        """
        获取角落区域的掩码

        Args:
            radius: 检查半径
            corner_name: 角落名称

        Returns:
            角落区域掩码
        """
        mask = np.zeros((radius * 2, radius * 2), dtype=np.uint8)

        if corner_name == "top_left":
            mask[radius:, radius:] = 255
        elif corner_name == "top_right":
            mask[radius:, :radius] = 255
        elif corner_name == "bottom_left":
            mask[:radius, radius:] = 255
        elif corner_name == "bottom_right":
            mask[:radius, :radius] = 255

        return mask

    def _calculate_missing_area(self, contour: np.ndarray, corner_x: int, corner_y: int) -> float:
        """计算缺失区域的大小"""
        # 简单返回一个估算值
        return 50.0

    def _fallback_angle_detection(self, contour: np.ndarray) -> List[Dict[str, Any]]:
        """回退的角度检测方法"""
        folded_corners = []

        # 简单的角度检测
        for i in range(len(contour)):
            if i < 2 or i >= len(contour) - 2:
                continue

            p1 = contour[i - 2][0]
            p2 = contour[i][0]
            p3 = contour[i + 2][0]

            angle = self._calculate_angle(p1, p2, p3)

            if angle < 60:  # 比较尖锐的角度
                folded_corners.append({
                    "position": p2.tolist(),
                    "angle": angle,
                    "corner_type": "sharp_angle",
                    "size": 30.0
                })

        return folded_corners

    def _calculate_angle(self, p1: np.ndarray, p2: np.ndarray, p3: np.ndarray) -> float:
        """
        计算三点之间的角度
        
        Args:
            p1, p2, p3: 三个点的坐标
            
        Returns:
            角度（度）
        """
        v1 = p1 - p2
        v2 = p3 - p2
        
        cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
        cos_angle = np.clip(cos_angle, -1.0, 1.0)  # 防止数值误差
        
        angle_rad = np.arccos(cos_angle)
        angle_deg = np.degrees(angle_rad)
        
        return angle_deg

    def _calculate_confidence(self, folded_corners: List[Dict[str, Any]], contour: np.ndarray) -> float:
        """
        计算折角检测的置信度

        Args:
            folded_corners: 检测到的折角列表
            contour: 文档轮廓

        Returns:
            置信度 (0.0 - 1.0)
        """
        if not folded_corners:
            return 0.0

        # 基于折角数量和类型计算置信度
        total_score = 0.0

        for corner in folded_corners:
            corner_type = corner.get("corner_type", "unknown")
            size = corner.get("size", 0)

            # 根据角落类型给分
            if corner_type == "missing_corner":
                type_score = 0.8  # 缺失角落的置信度较高
            elif corner_type == "sharp_angle":
                angle = corner.get("angle", 90)
                type_score = 1.0 - abs(angle - 90) / 90.0  # 角度异常程度
            else:
                type_score = 0.5

            # 尺寸权重
            size_score = min(size / 100.0, 1.0)

            corner_score = (type_score + size_score) / 2.0
            total_score += corner_score

        # 归一化到 [0, 1] 范围
        confidence = min(total_score / len(folded_corners), 1.0)

        return confidence

    async def _detect_single_file(self, file_path: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """检测单个文件的折角"""
        try:
            # 检查文件格式
            file_ext = Path(file_path).suffix.lower()
            if file_ext not in self.supported_formats:
                return {
                    "file_path": file_path,
                    "has_folded_corners": False,
                    "folded_corners": [],
                    "confidence": 0.0,
                    "needs_correction": False,
                    "error": f"不支持的文件格式: {file_ext}"
                }

            # 读取图像
            image = cv2.imread(file_path)
            if image is None:
                return {
                    "file_path": file_path,
                    "has_folded_corners": False,
                    "folded_corners": [],
                    "confidence": 0.0,
                    "needs_correction": False,
                    "error": "无法读取图像文件"
                }

            # 检测折角
            result = await self._detect_single_image(image, Path(file_path).name, options)
            result["file_path"] = file_path

            return result

        except Exception as e:
            logger.error(f"检测文件 {file_path} 折角失败: {str(e)}")
            return {
                "file_path": file_path,
                "has_folded_corners": False,
                "folded_corners": [],
                "confidence": 0.0,
                "needs_correction": False,
                "error": str(e)
            }

    async def _detect_folder(self, folder_path: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """检测文件夹中所有图像的折角"""
        try:
            folder = Path(folder_path)

            # 遍历文件夹，找到所有支持的图像文件
            image_files = []
            for file_path in folder.rglob("*"):
                if file_path.is_file() and file_path.suffix.lower() in self.supported_formats:
                    image_files.append(str(file_path))

            if not image_files:
                return {
                    "folder_path": folder_path,
                    "total_files": 0,
                    "folded_count": 0,
                    "folded_files": [],
                    "normal_files": [],
                    "error_files": [],
                    "summary": "文件夹中没有找到支持的图像文件"
                }

            logger.info(f"开始检测文件夹 {folder_path} 中的 {len(image_files)} 个图像文件")

            # 检测每个文件
            folded_files = []
            normal_files = []
            error_files = []

            for file_path in image_files:
                result = await self._detect_single_file(file_path, options)

                if "error" in result:
                    error_files.append(result)
                elif result["needs_correction"]:
                    folded_files.append(result)
                else:
                    normal_files.append(result)

            # 统计结果
            total_files = len(image_files)
            folded_count = len(folded_files)

            logger.info(f"文件夹检测完成: 总计{total_files}个文件, 折角文件{folded_count}个")

            return {
                "folder_path": folder_path,
                "total_files": total_files,
                "folded_count": folded_count,
                "folded_files": folded_files,
                "normal_files": normal_files,
                "error_files": error_files,
                "summary": f"检测完成: {total_files}个文件中发现{folded_count}个折角文件"
            }

        except Exception as e:
            logger.error(f"检测文件夹 {folder_path} 失败: {str(e)}")
            return {
                "folder_path": folder_path,
                "total_files": 0,
                "folded_count": 0,
                "folded_files": [],
                "normal_files": [],
                "error_files": [],
                "error": str(e)
            }

    async def correct(self, input_data: Union[str, np.ndarray], options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        修复折角

        Args:
            input_data: 输入数据，可以是文件路径、文件夹路径或图像数组
            options: 修复选项

        Returns:
            修复结果字典
        """
        if options is None:
            options = {}

        logger.info("开始折角修复")

        if isinstance(input_data, np.ndarray):
            # 直接处理图像数组
            return await self._correct_single_image(input_data, "image_array", options)

        elif isinstance(input_data, str):
            path = Path(input_data)

            if path.is_file():
                return await self._correct_single_file(str(path), options)
            elif path.is_dir():
                return await self._correct_folder(str(path), options)
            else:
                raise FileNotFoundError(f"路径不存在: {input_data}")

        else:
            raise ValueError(f"不支持的输入类型: {type(input_data)}")

    async def _correct_single_image(self, image: np.ndarray, image_name: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """修复单张图像的折角"""
        try:
            # 先检测折角
            detection_result = await self._detect_single_image(image, image_name, options)

            if not detection_result["needs_correction"]:
                logger.info(f"图像 {image_name} 无需修复折角")
                return {
                    "image_name": image_name,
                    "correction_applied": False,
                    "detected_corners": detection_result["folded_corners"],
                    "confidence": detection_result["confidence"],
                    "corrected_image": image,
                    "original_size": image.shape,
                    "corrected_size": image.shape
                }

            # 执行折角修复
            corrected_image = self._repair_folded_corners(
                image,
                detection_result["folded_corners"],
                detection_result.get("document_contour")
            )

            logger.info(f"图像 {image_name} 折角修复完成")

            return {
                "image_name": image_name,
                "correction_applied": True,
                "detected_corners": detection_result["folded_corners"],
                "confidence": detection_result["confidence"],
                "corrected_image": corrected_image,
                "original_size": image.shape,
                "corrected_size": corrected_image.shape
            }

        except Exception as e:
            logger.error(f"修复图像 {image_name} 折角失败: {str(e)}")
            return {
                "image_name": image_name,
                "correction_applied": False,
                "detected_corners": [],
                "confidence": 0.0,
                "error": str(e)
            }

    def _repair_folded_corners(self, image: np.ndarray, folded_corners: List[Dict[str, Any]],
                              document_contour: List = None) -> np.ndarray:
        """
        修复图像中的折角

        Args:
            image: 原始图像
            folded_corners: 检测到的折角信息
            document_contour: 文档轮廓

        Returns:
            修复后的图像
        """
        corrected_image = image.copy()

        for corner in folded_corners:
            position = corner["position"]
            size = corner["size"]

            # 创建折角区域的掩码
            mask = self._create_corner_mask(corrected_image, position, size)

            # 使用图像修复技术填充折角区域
            corrected_image = cv2.inpaint(corrected_image, mask, inpaintRadius=3, flags=cv2.INPAINT_TELEA)

        return corrected_image

    def _create_corner_mask(self, image: np.ndarray, position: List[int], size: float) -> np.ndarray:
        """
        创建折角区域的掩码

        Args:
            image: 图像
            position: 折角位置 [x, y]
            size: 折角尺寸

        Returns:
            掩码图像
        """
        height, width = image.shape[:2]
        mask = np.zeros((height, width), dtype=np.uint8)

        # 在折角位置创建圆形掩码
        center = (int(position[0]), int(position[1]))
        radius = int(size / 4)  # 使用折角尺寸的1/4作为修复半径

        cv2.circle(mask, center, radius, 255, -1)

        return mask

    async def _correct_single_file(self, file_path: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """修复单个文件的折角"""
        try:
            # 检查文件格式
            file_ext = Path(file_path).suffix.lower()
            if file_ext not in self.supported_formats:
                return {
                    "file_path": file_path,
                    "correction_applied": False,
                    "error": f"不支持的文件格式: {file_ext}"
                }

            # 读取图像
            image = cv2.imread(file_path)
            if image is None:
                return {
                    "file_path": file_path,
                    "correction_applied": False,
                    "error": "无法读取图像文件"
                }

            # 修复折角
            result = await self._correct_single_image(image, Path(file_path).name, options)

            if not result["correction_applied"]:
                logger.info(f"文件 {file_path} 无需修复折角")
                return {
                    "file_path": file_path,
                    "correction_applied": False,
                    "detected_corners": result["detected_corners"],
                    "confidence": result["confidence"]
                }

            # 生成保存路径 - 自动命名策略
            overwrite = options.get("overwrite", False)

            if overwrite:
                corrected_path = file_path
            else:
                corrected_path = self._generate_corrected_filename(file_path)

            # 保存修复后的图像
            success = self.image_utils.save_image_to_path(result["corrected_image"], corrected_path)

            if success:
                logger.info(f"文件修复完成: {file_path} -> {corrected_path}")
                return {
                    "file_path": file_path,
                    "corrected_file": corrected_path,
                    "correction_applied": True,
                    "detected_corners": result["detected_corners"],
                    "confidence": result["confidence"],
                    "original_size": result["original_size"],
                    "corrected_size": result["corrected_size"]
                }
            else:
                return {
                    "file_path": file_path,
                    "correction_applied": False,
                    "detected_corners": result["detected_corners"],
                    "confidence": result["confidence"],
                    "error": "保存修复后的图像失败"
                }

        except Exception as e:
            logger.error(f"修复文件 {file_path} 失败: {str(e)}")
            return {
                "file_path": file_path,
                "correction_applied": False,
                "error": str(e)
            }

    def _generate_corrected_filename(self, original_path: str) -> str:
        """
        自动生成修复后的文件名

        Args:
            original_path: 原始文件路径

        Returns:
            修复后的文件路径
        """
        file_path_obj = Path(original_path).resolve()

        # 简单的命名：原名_corner_fixed.扩展名
        corrected_name = f"{file_path_obj.stem}_corner_fixed{file_path_obj.suffix}"
        corrected_path = str(file_path_obj.parent / corrected_name)

        # 如果文件已存在，删除它（说明之前修复错了）
        if Path(corrected_path).exists():
            Path(corrected_path).unlink()

        return corrected_path
