"""
图像相似度检测模块
支持文件夹扫描和自动相似度分组
"""

import cv2
import numpy as np
import os
import glob
from typing import Dict, Any, List, Tuple
import hashlib
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.feature_extraction import image as skimage
import logging
from pathlib import Path

from app.utils.logger import setup_logger

logger = setup_logger(__name__)


class SimilarityDetector:
    """图像相似度检测器"""
    
    def __init__(self):
        # 初始化SIFT特征检测器
        self.sift = cv2.SIFT_create()
        # 初始化ORB特征检测器
        self.orb = cv2.ORB_create()
        # 初始化FLANN匹配器
        FLANN_INDEX_KDTREE = 1
        index_params = dict(algorithm=FLANN_INDEX_KDTREE, trees=5)
        search_params = dict(checks=50)
        self.flann = cv2.FlannBasedMatcher(index_params, search_params)
        
        # 支持的图像格式
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
        
    async def scan_folder_and_group_similar(self, folder_path: str, 
                                          method: str = "hybrid",
                                          threshold: float = 0.8,
                                          options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        扫描文件夹并自动分组相似图片
        
        Args:
            folder_path: 文件夹路径
            method: 检测方法 ("hash", "histogram", "sift", "orb", "hybrid")
            threshold: 相似度阈值
            options: 配置选项
        
        Returns:
            相似度分组结果
        """
        if options is None:
            options = {}
        
        try:
            # 扫描文件夹中的所有图片
            image_files = self._scan_image_files(folder_path)
            logger.info(f"在文件夹 {folder_path} 中找到 {len(image_files)} 张图片")
            
            if len(image_files) < 2:
                return {
                    "error": "文件夹中图片数量不足，至少需要2张图片",
                    "total_images": len(image_files)
                }
            
            # 加载所有图片
            images_data = []
            for file_path in image_files:
                try:
                    image = cv2.imread(file_path)
                    if image is not None:
                        images_data.append({
                            "path": file_path,
                            "filename": os.path.basename(file_path),
                            "image": image,
                            "size": image.shape[:2]
                        })
                    else:
                        logger.warning(f"无法加载图片: {file_path}")
                except Exception as e:
                    logger.error(f"加载图片失败 {file_path}: {str(e)}")
            
            if len(images_data) < 2:
                return {
                    "error": "成功加载的图片数量不足，至少需要2张图片",
                    "total_images": len(images_data)
                }
            
            # 进行相似度分组
            return await self._group_similar_images(images_data, method, threshold, options)
            
        except Exception as e:
            logger.error(f"扫描文件夹失败: {str(e)}")
            return {"error": f"扫描文件夹失败: {str(e)}"}
    
    def _scan_image_files(self, folder_path: str) -> List[str]:
        """扫描文件夹中的图片文件"""
        image_files = []
        
        # 确保路径存在
        if not os.path.exists(folder_path):
            raise ValueError(f"文件夹不存在: {folder_path}")
        
        # 递归扫描所有子文件夹
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                file_path = os.path.join(root, file)
                file_ext = os.path.splitext(file)[1].lower()
                
                if file_ext in self.supported_formats:
                    image_files.append(file_path)
        
        return sorted(image_files)
    
    async def _group_similar_images(self, images_data: List[Dict],
                                   method: str, threshold: float,
                                   options: Dict[str, Any]) -> Dict[str, Any]:
        """对图片进行相似度分组"""

        n = len(images_data)
        similarity_matrix = np.zeros((n, n))

        # 设置对角线为1.0（自己与自己的相似度）
        np.fill_diagonal(similarity_matrix, 1.0)

        # 计算相似度矩阵
        logger.info("开始计算相似度矩阵...")
        for i in range(n):
            for j in range(i+1, n):
                similarity_result = await self.detect_similarity(
                    images_data[i]["image"],
                    images_data[j]["image"],
                    method,
                    options
                )

                if method == "hybrid" and "overall_similarity" in similarity_result:
                    similarity = similarity_result["overall_similarity"]
                else:
                    # 取第一个可用的相似度
                    similarity = 0.0
                    for key, value in similarity_result.items():
                        if isinstance(value, dict) and "similarity" in value:
                            similarity = value["similarity"]
                            break

                similarity_matrix[i][j] = similarity
                similarity_matrix[j][i] = similarity

        # 使用连通分量算法进行全量相似度分组
        similar_groups = self._find_connected_components(similarity_matrix, threshold, images_data)

        # 统计信息
        all_similarities = similarity_matrix[np.triu_indices(n, k=1)]

        return {
            "folder_path": images_data[0]["path"].rsplit(os.sep, 1)[0] if images_data else "",
            "total_images": n,
            "similar_groups": similar_groups,
            "similarity_matrix": similarity_matrix.tolist(),
            "statistics": {
                "total_images": n,
                "similar_groups_count": len(similar_groups),
                "avg_similarity": np.mean(all_similarities) if len(all_similarities) > 0 else 0.0,
                "max_similarity": np.max(similarity_matrix),
                "min_similarity": np.min(similarity_matrix),
                "threshold_used": threshold,
                "method_used": method
            },
            "image_info": [
                {
                    "index": i,
                    "filename": img["filename"],
                    "path": img["path"],
                    "size": img["size"]
                }
                for i, img in enumerate(images_data)
            ]
        }

    def _find_connected_components(self, similarity_matrix: np.ndarray, threshold: float,
                                  images_data: List[Dict]) -> List[Dict[str, Any]]:
        """
        使用连通分量算法找出所有相似图片组
        这样可以确保全量检测，即使图片A与图片C相似，图片B与图片C相似，
        但图片A与图片B不够相似，它们也会被分到同一组
        """
        n = len(similarity_matrix)
        visited = [False] * n
        similar_groups = []

        def dfs(node: int, current_group: List[int]):
            """深度优先搜索找出连通分量"""
            visited[node] = True
            current_group.append(node)

            # 遍历所有与当前节点相似的节点
            for neighbor in range(n):
                if not visited[neighbor] and similarity_matrix[node][neighbor] >= threshold:
                    dfs(neighbor, current_group)

        # 遍历所有节点，找出连通分量
        for i in range(n):
            if not visited[i]:
                current_group = []
                dfs(i, current_group)

                # 只有包含多个图片的组才算作相似组
                if len(current_group) > 1:
                    # 计算组内相似度统计
                    group_similarities = []
                    for idx1 in current_group:
                        for idx2 in current_group:
                            if idx1 != idx2:
                                group_similarities.append(similarity_matrix[idx1][idx2])

                    similar_groups.append({
                        "indices": current_group,
                        "size": len(current_group),
                        "files": [images_data[idx]["filename"] for idx in current_group],
                        "paths": [images_data[idx]["path"] for idx in current_group],
                        "avg_similarity": np.mean(group_similarities) if group_similarities else 0.0,
                        "max_similarity": np.max(group_similarities) if group_similarities else 0.0,
                        "min_similarity": np.min(group_similarities) if group_similarities else 0.0
                    })

        return similar_groups
    
    async def detect_similarity(self, image1: np.ndarray, image2: np.ndarray, 
                               method: str = "hybrid", options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        检测两张图像的相似度
        
        Args:
            image1: 第一张图像
            image2: 第二张图像
            method: 检测方法 ("hash", "histogram", "sift", "orb", "hybrid")
            options: 配置选项
        
        Returns:
            相似度检测结果
        """
        if options is None:
            options = {}
        
        # 预处理图像
        img1_processed = self._preprocess_image(image1)
        img2_processed = self._preprocess_image(image2)
        
        results = {}
        
        if method == "hash" or method == "hybrid":
            results["hash_similarity"] = await self._hash_similarity(img1_processed, img2_processed)
        
        if method == "histogram" or method == "hybrid":
            results["histogram_similarity"] = await self._histogram_similarity(img1_processed, img2_processed)
        
        if method == "sift" or method == "hybrid":
            results["sift_similarity"] = await self._sift_similarity(img1_processed, img2_processed, options)
        
        if method == "orb" or method == "hybrid":
            results["orb_similarity"] = await self._orb_similarity(img1_processed, img2_processed, options)
        
        if method == "hybrid":
            # 综合相似度计算
            results["overall_similarity"] = self._calculate_hybrid_similarity(results)
        
        return results
    
    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """预处理图像"""
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # 统一尺寸 (256x256)
        resized = cv2.resize(gray, (256, 256))
        
        # 高斯模糊去噪
        blurred = cv2.GaussianBlur(resized, (5, 5), 0)
        
        return blurred
    
    async def _hash_similarity(self, img1: np.ndarray, img2: np.ndarray) -> Dict[str, Any]:
        """感知哈希相似度"""
        try:
            # 计算感知哈希
            hash1 = self._calculate_perceptual_hash(img1)
            hash2 = self._calculate_perceptual_hash(img2)
            
            # 计算汉明距离
            hamming_distance = bin(hash1 ^ hash2).count('1')
            similarity = 1 - (hamming_distance / 64)  # 64位哈希
            
            return {
                "similarity": similarity,
                "hamming_distance": hamming_distance,
                "hash1": hash1,
                "hash2": hash2
            }
        except Exception as e:
            logger.error(f"哈希相似度计算失败: {str(e)}")
            return {"similarity": 0.0, "error": str(e)}
    
    def _calculate_perceptual_hash(self, image: np.ndarray) -> int:
        """计算感知哈希"""
        # 缩放到8x8
        small = cv2.resize(image, (8, 8))
        
        # 转换为浮点数
        gray = small.astype(np.float64)
        
        # 计算DCT
        dct = cv2.dct(gray)
        
        # 取左上角8x8的低频部分
        dct_low = dct[:8, :8]
        
        # 计算平均值
        avg = np.mean(dct_low)
        
        # 生成哈希
        hash_value = 0
        for i in range(8):
            for j in range(8):
                if dct_low[i, j] > avg:
                    hash_value |= 1 << (i * 8 + j)
        
        return hash_value
    
    async def _histogram_similarity(self, img1: np.ndarray, img2: np.ndarray) -> Dict[str, Any]:
        """直方图相似度"""
        try:
            # 计算直方图
            hist1 = cv2.calcHist([img1], [0], None, [256], [0, 256])
            hist2 = cv2.calcHist([img2], [0], None, [256], [0, 256])
            
            # 归一化
            cv2.normalize(hist1, hist1, 0, 1, cv2.NORM_MINMAX)
            cv2.normalize(hist2, hist2, 0, 1, cv2.NORM_MINMAX)
            
            # 计算相似度
            correlation = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)
            chi_square = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CHISQR)
            intersection = cv2.compareHist(hist1, hist2, cv2.HISTCMP_INTERSECT)
            
            return {
                "correlation": correlation,
                "chi_square": chi_square,
                "intersection": intersection,
                "similarity": (correlation + 1) / 2  # 转换为0-1范围
            }
        except Exception as e:
            logger.error(f"直方图相似度计算失败: {str(e)}")
            return {"similarity": 0.0, "error": str(e)}
    
    async def _sift_similarity(self, img1: np.ndarray, img2: np.ndarray,
                              options: Dict[str, Any]) -> Dict[str, Any]:
        """SIFT特征相似度"""
        try:
            # 从options中获取配置参数
            ratio_threshold = options.get('sift_ratio_threshold', 0.7)
            min_match_count = options.get('sift_min_match_count', 10)

            # 检测SIFT特征
            kp1, des1 = self.sift.detectAndCompute(img1, None)
            kp2, des2 = self.sift.detectAndCompute(img2, None)

            if des1 is None or des2 is None or len(des1) < 2 or len(des2) < 2:
                return {"similarity": 0.0, "matches": 0, "error": "无法检测到足够的特征点"}

            # 特征匹配
            matches = self.flann.knnMatch(des1, des2, k=2)

            # 应用Lowe's ratio test
            good_matches = []
            for match_pair in matches:
                if len(match_pair) == 2:
                    m, n = match_pair
                    if m.distance < ratio_threshold * n.distance:
                        good_matches.append(m)

            # 计算相似度 - 改进的归一化方法
            max_possible_matches = min(len(kp1), len(kp2))
            if max_possible_matches > 0:
                # 确保相似度不超过1.0，并考虑最小匹配数量要求
                raw_similarity = len(good_matches) / max_possible_matches
                similarity = min(1.0, raw_similarity)

                # 如果好的匹配数量太少，降低相似度
                if len(good_matches) < min_match_count:
                    similarity *= (len(good_matches) / min_match_count)
            else:
                similarity = 0.0

            return {
                "similarity": similarity,
                "total_matches": len(matches),
                "good_matches": len(good_matches),
                "keypoints1": len(kp1),
                "keypoints2": len(kp2),
                "ratio_threshold": ratio_threshold
            }
        except Exception as e:
            logger.error(f"SIFT相似度计算失败: {str(e)}")
            return {"similarity": 0.0, "error": str(e)}
    
    async def _orb_similarity(self, img1: np.ndarray, img2: np.ndarray,
                             options: Dict[str, Any]) -> Dict[str, Any]:
        """ORB特征相似度"""
        try:
            # 从options中获取配置参数
            max_distance = options.get('orb_max_distance', 50)
            min_match_count = options.get('orb_min_match_count', 10)

            # 检测ORB特征
            kp1, des1 = self.orb.detectAndCompute(img1, None)
            kp2, des2 = self.orb.detectAndCompute(img2, None)

            if des1 is None or des2 is None:
                return {"similarity": 0.0, "matches": 0, "error": "无法检测到特征点"}

            # 创建BF匹配器
            bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
            matches = bf.match(des1, des2)

            # 按距离排序并过滤距离过大的匹配
            matches = sorted(matches, key=lambda x: x.distance)
            good_matches = [m for m in matches if m.distance <= max_distance]

            # 计算相似度 - 改进的归一化方法
            max_possible_matches = min(len(kp1), len(kp2))
            if max_possible_matches > 0:
                # 确保相似度不超过1.0
                raw_similarity = len(good_matches) / max_possible_matches
                similarity = min(1.0, raw_similarity)

                # 如果好的匹配数量太少，降低相似度
                if len(good_matches) < min_match_count:
                    similarity *= (len(good_matches) / min_match_count)
            else:
                similarity = 0.0

            return {
                "similarity": similarity,
                "total_matches": len(matches),
                "good_matches": len(good_matches),
                "keypoints1": len(kp1),
                "keypoints2": len(kp2),
                "avg_distance": np.mean([m.distance for m in good_matches]) if good_matches else 0,
                "max_distance_threshold": max_distance
            }
        except Exception as e:
            logger.error(f"ORB相似度计算失败: {str(e)}")
            return {"similarity": 0.0, "error": str(e)}
    
    def _calculate_hybrid_similarity(self, results: Dict[str, Any]) -> float:
        """计算综合相似度"""
        # 调整权重：Hash和Histogram对剪切/旋转更敏感，给更高权重
        weights = {
            "hash_similarity": 0.4,      # 感知哈希对几何变换鲁棒
            "histogram_similarity": 0.4,  # 直方图对剪切/旋转鲁棒
            "sift_similarity": 0.1,      # SIFT对预处理敏感，降低权重
            "orb_similarity": 0.1        # ORB表现不佳，降低权重
        }
        
        total_weight = 0
        weighted_sum = 0
        
        for method, weight in weights.items():
            if method in results and "similarity" in results[method]:
                similarity = results[method]["similarity"]
                weighted_sum += similarity * weight
                total_weight += weight
        
        if total_weight > 0:
            return weighted_sum / total_weight
        else:
            return 0.0 