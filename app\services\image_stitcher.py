"""
图像拼接模块
将多张图像拼接成长图
"""

import numpy as np
from typing import List, Dict, Any

from app.utils.logger import setup_logger

logger = setup_logger(__name__)


class ImageStitcher:
    """图像拼接器"""

    def __init__(self):
        pass

    async def stitch(self, images: List[np.ndarray], options: Dict[str, Any] = None) -> np.ndarray:
        """拼接多张图像"""
        # 具体实现待补充
        return images[0] if images else np.array([])
