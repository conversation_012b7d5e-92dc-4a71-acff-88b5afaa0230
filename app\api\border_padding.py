"""
图像补边API端点
"""

from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from typing import Optional, Union, List
import os
import tempfile
import json
from pathlib import Path

from app.services.border_padding import BorderPadding
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

router = APIRouter(prefix="/border-padding", tags=["border-padding"])

# 初始化补边处理器
border_padding = BorderPadding()


@router.post("/add-border")
async def add_border_to_image(
    file: UploadFile = File(...),
    target_size: Optional[str] = Form(None),  # JSON字符串 "[width, height]"
    border_size: Optional[str] = Form(None),  # JSON字符串，支持多种格式
    unit: str = Form("pixels"),
    dpi: int = Form(300),
    color_mode: str = Form("white"),
    custom_color: Optional[str] = Form(None)  # JSON字符串 "[r, g, b]"
):
    """
    为单张图像添加边框
    
    Args:
        file: 上传的图像文件
        target_size: 目标尺寸 JSON字符串 "[width, height]"
        border_size: 边框大小 JSON字符串，支持:
            - "50" (四边相同)
            - "[horizontal, vertical]" (水平垂直)
            - "[top, right, bottom, left]" (四边不同)
        unit: 单位 ("pixels" 或 "mm")
        dpi: DPI设置
        color_mode: 颜色模式 ("white", "black", "edge_color", "dominant_color", "custom")
        custom_color: 自定义颜色 JSON字符串 "[r, g, b]"
    
    Returns:
        处理结果和文件下载链接
    """
    try:
        # 验证文件类型
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="文件必须是图像格式")
        
        # 解析参数
        parsed_target_size = None
        if target_size:
            try:
                parsed_target_size = tuple(json.loads(target_size))
                if len(parsed_target_size) != 2:
                    raise ValueError("target_size必须包含两个值")
            except (json.JSONDecodeError, ValueError) as e:
                raise HTTPException(status_code=400, detail=f"target_size格式错误: {e}")
        
        parsed_border_size = None
        if border_size:
            try:
                border_data = json.loads(border_size)
                if isinstance(border_data, (int, float)):
                    parsed_border_size = int(border_data)
                elif isinstance(border_data, list):
                    if len(border_data) == 2:
                        parsed_border_size = tuple(map(int, border_data))
                    elif len(border_data) == 4:
                        parsed_border_size = tuple(map(int, border_data))
                    else:
                        raise ValueError("border_size列表长度必须是2或4")
                else:
                    raise ValueError("border_size格式错误")
            except (json.JSONDecodeError, ValueError) as e:
                raise HTTPException(status_code=400, detail=f"border_size格式错误: {e}")
        
        parsed_custom_color = None
        if custom_color:
            try:
                color_data = json.loads(custom_color)
                if len(color_data) != 3:
                    raise ValueError("custom_color必须包含三个值(R,G,B)")
                parsed_custom_color = tuple(map(int, color_data))
            except (json.JSONDecodeError, ValueError) as e:
                raise HTTPException(status_code=400, detail=f"custom_color格式错误: {e}")
        
        # 验证参数
        if not parsed_target_size and not parsed_border_size:
            raise HTTPException(status_code=400, detail="必须指定target_size或border_size")
        
        if unit not in ["pixels", "mm"]:
            raise HTTPException(status_code=400, detail="unit必须是'pixels'或'mm'")
        
        if color_mode not in ["white", "black", "edge_color", "dominant_color", "custom"]:
            raise HTTPException(status_code=400, detail="color_mode值无效")
        
        if color_mode == "custom" and not parsed_custom_color:
            raise HTTPException(status_code=400, detail="使用custom颜色模式时必须提供custom_color")
        
        # 保存上传的文件到临时目录
        with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.filename).suffix) as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_input_path = temp_file.name
        
        try:
            # 处理图像
            result = await border_padding.add_border(
                image_path=temp_input_path,
                target_size=parsed_target_size,
                border_size=parsed_border_size,
                unit=unit,
                dpi=dpi,
                color_mode=color_mode,
                custom_color=parsed_custom_color
            )
            
            if not result.get("success"):
                raise HTTPException(status_code=500, detail=result.get("error", "处理失败"))
            
            # 读取处理后的文件
            output_path = result["output_file"]
            if not os.path.exists(output_path):
                raise HTTPException(status_code=500, detail="输出文件不存在")
            
            # 返回结果（实际应用中可能需要返回文件或文件URL）
            return {
                "success": True,
                "message": "补边处理完成",
                "original_filename": file.filename,
                "result": result,
                "download_url": f"/download/{Path(output_path).name}"  # 需要实现下载端点
            }
            
        finally:
            # 清理临时文件
            if os.path.exists(temp_input_path):
                os.unlink(temp_input_path)
                
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"补边API处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.post("/batch-add-border")
async def batch_add_border_to_folder(
    folder_path: str = Form(...),
    target_size: Optional[str] = Form(None),
    border_size: Optional[str] = Form(None),
    unit: str = Form("pixels"),
    dpi: int = Form(300),
    color_mode: str = Form("white"),
    custom_color: Optional[str] = Form(None)
):
    """
    批量为文件夹中的图像添加边框
    
    Args:
        folder_path: 文件夹路径
        其他参数同add_border_to_image
    
    Returns:
        批量处理结果
    """
    try:
        # 验证文件夹
        if not os.path.exists(folder_path):
            raise HTTPException(status_code=400, detail=f"文件夹不存在: {folder_path}")
        
        if not os.path.isdir(folder_path):
            raise HTTPException(status_code=400, detail=f"路径不是文件夹: {folder_path}")
        
        # 解析参数（复用单张图像的参数解析逻辑）
        parsed_target_size = None
        if target_size:
            try:
                parsed_target_size = tuple(json.loads(target_size))
            except (json.JSONDecodeError, ValueError) as e:
                raise HTTPException(status_code=400, detail=f"target_size格式错误: {e}")
        
        parsed_border_size = None
        if border_size:
            try:
                border_data = json.loads(border_size)
                if isinstance(border_data, (int, float)):
                    parsed_border_size = int(border_data)
                elif isinstance(border_data, list):
                    parsed_border_size = tuple(map(int, border_data))
            except (json.JSONDecodeError, ValueError) as e:
                raise HTTPException(status_code=400, detail=f"border_size格式错误: {e}")
        
        parsed_custom_color = None
        if custom_color:
            try:
                parsed_custom_color = tuple(map(int, json.loads(custom_color)))
            except (json.JSONDecodeError, ValueError) as e:
                raise HTTPException(status_code=400, detail=f"custom_color格式错误: {e}")
        
        # 批量处理
        result = await border_padding.batch_add_border(
            folder_path=folder_path,
            target_size=parsed_target_size,
            border_size=parsed_border_size,
            unit=unit,
            dpi=dpi,
            color_mode=color_mode,
            custom_color=parsed_custom_color
        )
        
        if not result.get("success"):
            raise HTTPException(status_code=500, detail=result.get("error", "批量处理失败"))
        
        return {
            "success": True,
            "message": "批量补边处理完成",
            "result": result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量补边API处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.get("/supported-formats")
async def get_supported_formats():
    """获取支持的图像格式"""
    return {
        "supported_formats": border_padding.supported_formats,
        "color_modes": ["white", "black", "edge_color", "dominant_color", "custom"],
        "units": ["pixels", "mm"]
    }


@router.post("/preview-border-color")
async def preview_border_color(
    file: UploadFile = File(...),
    color_mode: str = Form("edge_color")
):
    """
    预览补边颜色
    
    Args:
        file: 上传的图像文件
        color_mode: 颜色模式
    
    Returns:
        预览的颜色值
    """
    try:
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="文件必须是图像格式")
        
        # 保存临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.filename).suffix) as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_path = temp_file.name
        
        try:
            import cv2
            image = cv2.imread(temp_path)
            if image is None:
                raise HTTPException(status_code=400, detail="无法读取图像文件")
            
            # 计算颜色
            color = border_padding._calculate_border_color(image, color_mode, None)
            
            return {
                "success": True,
                "color_mode": color_mode,
                "color_bgr": color,
                "color_rgb": [color[2], color[1], color[0]],  # 转换为RGB
                "color_hex": f"#{color[2]:02x}{color[1]:02x}{color[0]:02x}"  # 转换为十六进制
            }
            
        finally:
            if os.path.exists(temp_path):
                os.unlink(temp_path)
                
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"颜色预览失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")
