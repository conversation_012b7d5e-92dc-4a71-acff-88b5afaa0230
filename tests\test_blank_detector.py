"""
空白页检测测试
"""

import pytest
import numpy as np
import cv2
from app.services.blank_detector import BlankDetector


@pytest.mark.asyncio
async def test_blank_detector_white_image():
    """测试纯白图像检测"""
    detector = BlankDetector()
    
    # 创建纯白图像
    white_image = np.ones((500, 500, 3), dtype=np.uint8) * 255
    
    result = await detector.detect(white_image)
    
    assert result["is_blank"] == True
    assert result["confidence"] > 0.8


@pytest.mark.asyncio
async def test_blank_detector_text_image():
    """测试有文字的图像检测"""
    detector = BlankDetector()
    
    # 创建有文字的图像
    text_image = np.ones((500, 500, 3), dtype=np.uint8) * 255
    cv2.putText(text_image, "Hello World", (100, 250), 
                cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 0), 3)
    
    result = await detector.detect(text_image)
    
    assert result["is_blank"] == False
    assert result["confidence"] < 0.5


if __name__ == "__main__":
    import asyncio
    import os
    from pathlib import Path

    async def run_tests():
        detector = BlankDetector()

        # 检测testimage文件夹
        test_folder = "testimage"

        if not os.path.exists(test_folder):
            print(f"测试文件夹 {test_folder} 不存在，请先创建并放入测试图片")
            return

        print(f"开始检测文件夹: {test_folder}")
        print("=" * 50)

        result = await detector.detect(test_folder)

        # 打印检测结果
        if "error" in result:
            print(f"检测失败: {result['error']}")
            return

        print(f"检测完成!")
        print(f"总文件数: {result['total_files']}")
        print(f"空白页数: {result['blank_count']}")
        print(f"有效页数: {result['total_files'] - result['blank_count'] - len(result.get('error_files', []))}")

        if result.get('error_files'):
            print(f"错误文件数: {len(result['error_files'])}")

        print("\n" + "=" * 50)

        # 详细显示空白页
        if result['blank_files']:
            print("检测到的空白页:")
            for blank_file in result['blank_files']:
                file_name = Path(blank_file['file_path']).name
                confidence = blank_file['confidence']
                edge_ratio = blank_file['edge_ratio']
                bright_ratio = blank_file['bright_ratio']
                print(f"  📄 {file_name}")
                print(f"     置信度: {confidence:.3f}")
                print(f"     边缘比例: {edge_ratio:.4f}")
                print(f"     亮度比例: {bright_ratio:.4f}")
                print()
        else:
            print("未检测到空白页")

        print("\n" + "=" * 50)

        # 显示非空白页（前5个）
        if result['non_blank_files']:
            print("非空白页示例 (前5个):")
            for non_blank_file in result['non_blank_files'][:5]:
                file_name = Path(non_blank_file['file_path']).name
                confidence = non_blank_file['confidence']
                edge_ratio = non_blank_file['edge_ratio']
                bright_ratio = non_blank_file['bright_ratio']
                print(f"  📄 {file_name}")
                print(f"     置信度: {confidence:.3f}")
                print(f"     边缘比例: {edge_ratio:.4f}")
                print(f"     亮度比例: {bright_ratio:.4f}")
                print()

        # 显示错误文件
        if result.get('error_files'):
            print("\n错误文件:")
            for error_file in result['error_files']:
                file_name = Path(error_file['file_path']).name
                error_msg = error_file.get('error', '未知错误')
                print(f"  ❌ {file_name}: {error_msg}")

    asyncio.run(run_tests())
