"""
图像处理服务主模块
协调各个图像处理功能模块
"""

from typing import List, Dict, Any

from app.services.similarity_detector import SimilarityDetector
from app.services.blank_detector import BlankDetector
from app.services.skew_corrector import SkewCorrector
from app.services.border_handler import BorderHandler
from app.services.corner_corrector import CornerCorrector
from app.services.image_stitcher import ImageStitcher
from app.utils.image_utils import ImageUtils
from app.utils.logger import setup_logger

logger = setup_logger(__name__)


class ImageProcessorService:
    """图像处理服务主类"""
    
    def __init__(self):
        # 初始化各个处理模块
        self.similarity_detector = SimilarityDetector()
        self.blank_detector = BlankDetector()
        self.skew_corrector = SkewCorrector()
        self.border_handler = BorderHandler()
        self.corner_corrector = CornerCorrector()
        self.image_stitcher = ImageStitcher()
        self.image_utils = ImageUtils()
    
    async def process_image(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理图像的主入口方法
        
        Args:
            params: 处理参数
                - image_data: base64编码的图像数据
                - operations: 要执行的操作列表
                - options: 各操作的选项参数
        
        Returns:
            处理结果字典
        """
        try:
            # 解析输入参数
            image_data = params.get("image_data")
            operations = params.get("operations", [])
            options = params.get("options", {})
            
            if not image_data:
                raise ValueError("缺少图像数据")
            
            # 解码图像
            image = self.image_utils.decode_base64_image(image_data)
            logger.info(f"开始处理图像，尺寸: {image.shape}, 操作: {operations}")

            # 处理结果
            result = {
                "processed_image": None,
                "metadata": {
                    "original_size": image.shape[:2],
                    "operations_performed": [],
                    "processing_info": {}
                }
            }

            # 按顺序执行操作

            for operation in operations:
                logger.info(f"执行操作: {operation}")
                
                if operation == "detect_similarity":
                    # 相似度检测
                    if "reference_image" in params:
                        reference_image = self.image_utils.decode_base64_image(params["reference_image"])
                        similarity_result = await self.similarity_detector.detect_similarity(
                            processed_image, 
                            reference_image,
                            method=options.get("similarity_method", "hybrid"),
                            options=options.get("similarity_options", {})
                        )
                        result["metadata"]["processing_info"]["similarity_detection"] = similarity_result
                    else:
                        logger.warning("相似度检测需要提供参考图像")
                
                elif operation == "scan_folder_similarity":
                    # 扫描文件夹相似度检测
                    folder_path = params.get("folder_path")
                    if folder_path:
                        similarity_result = await self.similarity_detector.scan_folder_and_group_similar(
                            folder_path,
                            method=options.get("similarity_method", "hybrid"),
                            threshold=options.get("similarity_threshold", 0.8),
                            options=options.get("similarity_options", {})
                        )
                        result["metadata"]["processing_info"]["folder_similarity"] = similarity_result
                    else:
                        logger.warning("文件夹扫描需要提供folder_path参数")
                
                elif operation == "detect_blank":
                    # 空白页检测
                    blank_result = await self.blank_detector.detect(
                        processed_image,
                        options.get("blank_options", {})
                    )
                    result["metadata"]["processing_info"]["blank_detection"] = blank_result
                
                elif operation == "correct_skew":
                    # 倾斜矫正
                    processed_image, angle = await self.skew_corrector.correct(
                        processed_image,
                        options.get("skew_options", {})
                    )
                    result["metadata"]["processing_info"]["skew_correction"] = {
                        "angle": angle
                    }
                
                elif operation == "add_border":
                    # 添加边框
                    processed_image = await self.border_handler.add_border(
                        processed_image,
                        options.get("border_options", {})
                    )
                    result["metadata"]["processing_info"]["border_added"] = True
                
                elif operation == "fix_corner":
                    # 修复折角
                    corner_result = await self.corner_corrector.correct(
                        processed_image,
                        options.get("corner_options", {})
                    )
                    if corner_result.get("correction_applied", False):
                        processed_image = corner_result["corrected_image"]
                        result["metadata"]["processing_info"]["corner_fixed"] = True
                        result["metadata"]["processing_info"]["detected_corners"] = len(corner_result.get("detected_corners", []))
                    else:
                        result["metadata"]["processing_info"]["corner_fixed"] = False
                
                elif operation == "stitch_images":
                    # 图像拼接（需要多张图像）
                    images = params.get("images", [])
                    if len(images) > 1:
                        processed_image = await self.image_stitcher.stitch(
                            images,
                            options.get("stitch_options", {})
                        )
                        result["metadata"]["processing_info"]["images_stitched"] = len(images)
                
                else:
                    logger.warning(f"未知操作: {operation}")
                    continue
                
                result["metadata"]["operations_performed"].append(operation)
            
            # 编码处理后的图像
            result["processed_image"] = self.image_utils.encode_image_to_base64(processed_image)
            result["metadata"]["final_size"] = processed_image.shape[:2]
            
            logger.info(f"图像处理完成，执行了 {len(result['metadata']['operations_performed'])} 个操作")
            return result

        except Exception as e:
            logger.error(f"图像处理失败: {str(e)}")
            raise


    
    async def get_supported_operations(self) -> List[str]:
        """获取支持的操作列表"""
        return [
            "detect_similarity",
            "scan_folder_similarity",
            "detect_blank", 
            "correct_skew",
            "add_border",
            "fix_corner",
            "stitch_images"
        ]
