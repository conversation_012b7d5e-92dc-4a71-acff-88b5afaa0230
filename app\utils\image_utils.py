"""
图像工具模块
提供图像编解码和基础处理功能
"""

import base64
import cv2
import numpy as np
import tempfile
import os
from io import BytesIO
from PIL import Image
from typing import Union
from pathlib import Path

from app.core.exceptions import InvalidImageError, UnsupportedFormatError
from app.utils.logger import setup_logger

logger = setup_logger(__name__)


class ImageUtils:
    """图像工具类"""
    
    @staticmethod
    def decode_base64_image(base64_data: str) -> np.ndarray:
        """
        解码base64图像数据
        
        Args:
            base64_data: base64编码的图像数据
        
        Returns:
            OpenCV图像数组
        """
        try:
            # 移除base64前缀（如果存在）
            if ',' in base64_data:
                base64_data = base64_data.split(',')[1]
            
            # 解码base64
            image_bytes = base64.b64decode(base64_data)
            
            # 转换为numpy数组
            nparr = np.frombuffer(image_bytes, np.uint8)
            
            # 解码图像
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if image is None:
                raise InvalidImageError("无法解码图像数据")
            
            return image
            
        except Exception as e:
            logger.error(f"解码base64图像失败: {str(e)}")
            raise InvalidImageError(f"图像解码失败: {str(e)}")
    
    @staticmethod
    def encode_image_to_base64(image: np.ndarray, format: str = 'jpg') -> str:
        """
        编码图像为base64
        
        Args:
            image: OpenCV图像数组
            format: 输出格式
        
        Returns:
            base64编码的图像数据
        """
        try:
            # 编码图像
            if format.lower() in ['jpg', 'jpeg']:
                _, buffer = cv2.imencode('.jpg', image)
            elif format.lower() == 'png':
                _, buffer = cv2.imencode('.png', image)
            else:
                raise UnsupportedFormatError(f"不支持的格式: {format}")
            
            # 转换为base64
            image_base64 = base64.b64encode(buffer).decode('utf-8')
            
            return image_base64
            
        except Exception as e:
            logger.error(f"编码图像为base64失败: {str(e)}")
            raise
    
    @staticmethod
    def validate_image_size(image: np.ndarray, max_size: int = None) -> bool:
        """
        验证图像大小
        
        Args:
            image: 图像数组
            max_size: 最大尺寸（字节）
        
        Returns:
            是否符合大小要求
        """
        if max_size is None:
            from app.core.config import settings
            max_size = settings.MAX_IMAGE_SIZE
        
        image_size = image.nbytes
        return image_size <= max_size
    
    @staticmethod
    def get_image_info(image: np.ndarray) -> dict:
        """
        获取图像信息
        
        Args:
            image: 图像数组
        
        Returns:
            图像信息字典
        """
        height, width = image.shape[:2]
        channels = image.shape[2] if len(image.shape) > 2 else 1
        
        return {
            "width": width,
            "height": height,
            "channels": channels,
            "dtype": str(image.dtype),
            "size_bytes": image.nbytes
        }

    @staticmethod
    def get_temp_file_path(suffix: str = ".jpg") -> str:
        """
        获取系统临时文件路径

        Args:
            suffix: 文件后缀

        Returns:
            临时文件路径
        """
        return tempfile.mktemp(suffix=suffix)

    @staticmethod
    def create_backup_path(original_path: str, suffix: str = "_backup") -> str:
        """
        创建备份文件路径

        Args:
            original_path: 原始文件路径
            suffix: 备份后缀

        Returns:
            备份文件路径
        """
        path = Path(original_path)
        backup_name = f"{path.stem}{suffix}{path.suffix}"
        return str(path.parent / backup_name)

    @staticmethod
    def save_image_to_path(image: np.ndarray, file_path: str, quality: int = 95) -> bool:
        """
        保存图像到指定路径

        Args:
            image: 图像数组
            file_path: 保存路径
            quality: JPEG质量 (1-100)

        Returns:
            是否保存成功
        """
        try:
            # 确保目录存在
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)

            # 根据文件扩展名选择编码参数
            ext = Path(file_path).suffix.lower()
            if ext in ['.jpg', '.jpeg']:
                encode_params = [cv2.IMWRITE_JPEG_QUALITY, quality]
            elif ext == '.png':
                encode_params = [cv2.IMWRITE_PNG_COMPRESSION, 9]
            else:
                encode_params = []

            # 处理中文路径问题 - 使用cv2.imencode + 文件写入
            if any(ord(char) > 127 for char in file_path):
                # 包含非ASCII字符，使用编码方式
                if ext in ['.jpg', '.jpeg']:
                    encode_param = [cv2.IMWRITE_JPEG_QUALITY, quality]
                elif ext == '.png':
                    encode_param = [cv2.IMWRITE_PNG_COMPRESSION, 9]
                else:
                    encode_param = []

                success, encoded_img = cv2.imencode(ext, image, encode_param)
                if success:
                    with open(file_path, 'wb') as f:
                        f.write(encoded_img.tobytes())
                    return True
                else:
                    return False
            else:
                # 纯ASCII路径，直接使用cv2.imwrite
                success = cv2.imwrite(file_path, image, encode_params)
                return success

        except Exception as e:
            logger.error(f"保存图像失败: {str(e)}")
            return False
