"""
数据模型定义
定义WebSocket通信和图像处理的数据结构
"""

from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field


class ProcessRequest(BaseModel):
    """图像处理请求模型"""
    task_id: str = Field(..., description="任务ID")
    action: str = Field(..., description="操作类型")
    params: Dict[str, Any] = Field(default_factory=dict, description="处理参数")


class ProcessResponse(BaseModel):
    """图像处理响应模型"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="处理状态: success|error|processing")
    result: Optional[Dict[str, Any]] = Field(None, description="处理结果")
    error: Optional[str] = Field(None, description="错误信息")


class ImageProcessParams(BaseModel):
    """图像处理参数模型"""
    image_data: str = Field(..., description="base64编码的图像数据")
    operations: List[str] = Field(..., description="要执行的操作列表")
    options: Dict[str, Any] = Field(default_factory=dict, description="各操作的选项参数")



class ProcessResult(BaseModel):
    """处理结果模型"""
    processed_image: Optional[str] = Field(None, description="处理后的图像(base64)")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="处理元数据")


class DuplicateOptions(BaseModel):
    """重复页检测选项"""
    threshold: float = Field(0.95, description="相似度阈值")
    reference_images: List[str] = Field(default_factory=list, description="参考图像列表")
    method: str = Field("histogram", description="比较方法")


class BlankOptions(BaseModel):
    """空白页检测选项"""
    blank_threshold: float = Field(0.95, description="空白度阈值")
    white_threshold: int = Field(240, description="白色像素阈值")
    method: str = Field("brightness", description="检测方法")


class SkewOptions(BaseModel):
    """倾斜矫正选项"""
    method: str = Field("hough", description="检测方法")
    angle_threshold: float = Field(0.5, description="角度阈值")
    max_angle: float = Field(45, description="最大检测角度")


class BorderOptions(BaseModel):
    """边框处理选项"""
    size: int = Field(10, description="边框大小")
    color: tuple = Field((255, 255, 255), description="边框颜色")
    border_type: str = Field("solid", description="边框类型")


class CornerOptions(BaseModel):
    """折角修复选项"""
    method: str = Field("contour", description="检测方法")
    threshold: float = Field(0.1, description="检测阈值")
    repair_method: str = Field("inpaint", description="修复方法")


class StitchOptions(BaseModel):
    """图像拼接选项"""
    direction: str = Field("auto", description="拼接方向")
    method: str = Field("simple", description="拼接方法")
    overlap: float = Field(0.3, description="重叠检测阈值")
    blend: bool = Field(True, description="是否进行融合")
