"""
倾斜矫正模块
检测并矫正图像的倾斜角度
"""

import os
import cv2
import numpy as np
from typing import Dict, Any, List, Union, Tuple
from pathlib import Path
from collections import Counter

from app.utils.logger import setup_logger
from app.utils.image_utils import ImageUtils

logger = setup_logger(__name__)


class SkewCorrector:
    """倾斜矫正器"""

    def __init__(self):
        self.image_utils = ImageUtils()

        # 检测参数
        self.edge_threshold_low = 30   # 降低边缘检测阈值
        self.edge_threshold_high = 100
        self.hough_threshold = 80      # 适中的霍夫阈值
        self.angle_threshold = 2.0        # 小于此角度不进行矫正，提高阈值减少误检
        self.max_detection_angle = 45     # 最大检测角度范围

        # 支持的图像格式
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}

    async def detect(self, input_data: Union[str, np.ndarray], options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        检测倾斜角度（不修改文件）

        Args:
            input_data: 输入数据，可以是：
                - 图像数组 (np.ndarray)
                - 文件路径 (str)
                - 文件夹路径 (str)
            options: 检测选项

        Returns:
            检测结果字典
        """
        if options is None:
            options = {}

        logger.info("开始倾斜角度检测")

        # 判断输入类型
        if isinstance(input_data, np.ndarray):
            # 单张图像数组
            return await self._detect_single_image(input_data, "image_array", options)

        elif isinstance(input_data, str):
            # 文件或文件夹路径
            path = Path(input_data)

            if not path.exists():
                raise FileNotFoundError(f"路径不存在: {input_data}")

            if path.is_file():
                # 单个文件
                return await self._detect_single_file(str(path), options)

            elif path.is_dir():
                # 文件夹
                return await self._detect_folder(str(path), options)

        else:
            raise ValueError(f"不支持的输入类型: {type(input_data)}")

    async def correct(self, input_data: Union[str, np.ndarray], options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        检测并矫正倾斜角度

        Args:
            input_data: 输入数据
            options: 矫正选项
                - save_suffix: 保存文件后缀 (默认"_已纠偏")
                - overwrite: 是否覆盖原文件 (默认False)
                - angle_threshold: 角度阈值 (默认0.5度)

        Returns:
            矫正结果字典
        """
        if options is None:
            options = {}

        logger.info("开始倾斜角度矫正")

        # 判断输入类型
        if isinstance(input_data, np.ndarray):
            # 单张图像数组 - 只能返回矫正后的图像，无法保存文件
            return await self._correct_single_image(input_data, "image_array", options)

        elif isinstance(input_data, str):
            # 文件或文件夹路径
            path = Path(input_data)

            if not path.exists():
                raise FileNotFoundError(f"路径不存在: {input_data}")

            if path.is_file():
                # 单个文件
                return await self._correct_single_file(str(path), options)

            elif path.is_dir():
                # 文件夹
                return await self._correct_folder(str(path), options)

        else:
            raise ValueError(f"不支持的输入类型: {type(input_data)}")

    async def _detect_single_image(self, image: np.ndarray, image_name: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """检测单张图像的倾斜角度"""
        try:
            # 预处理：缩放图像以提高检测速度
            processed_image = self._preprocess_for_detection(image)

            # 检测倾斜角度
            angle, confidence = self._detect_skew_angle(processed_image)

            # 判断是否需要矫正
            needs_correction = abs(angle) >= self.angle_threshold

            logger.info(f"图像 {image_name}: 检测角度={angle:.2f}°, 置信度={confidence:.3f}, 需要矫正={needs_correction}")

            return {
                "image_name": image_name,
                "detected_angle": angle,
                "confidence": confidence,
                "needs_correction": needs_correction,
                "image_size": image.shape[:2]
            }

        except Exception as e:
            logger.error(f"检测图像 {image_name} 失败: {str(e)}")
            return {
                "image_name": image_name,
                "detected_angle": 0.0,
                "confidence": 0.0,
                "needs_correction": False,
                "error": str(e)
            }

    def _detect_skew_angle(self, image: np.ndarray) -> Tuple[float, float]:
        """
        检测图像倾斜角度的核心算法

        Args:
            image: 输入图像

        Returns:
            (检测到的角度, 置信度)
        """
        try:
            # 转换为灰度图
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image

            # 边缘检测
            edges = cv2.Canny(gray, self.edge_threshold_low, self.edge_threshold_high)

            # 使用HoughLinesP检测线段，获取长度信息
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=self.hough_threshold,
                                   minLineLength=50, maxLineGap=10)

            if lines is None or len(lines) == 0:
                logger.warning("未检测到足够的直线")
                return 0.0, 0.0

            # 提取角度并按线段长度加权
            weighted_angles = []

            for line in lines:
                x1, y1, x2, y2 = line[0]

                # 计算线段长度
                length = np.sqrt((x2 - x1)**2 + (y2 - y1)**2)

                # 过滤太短的线段（可能是噪声）
                if length < 30:
                    continue

                # 计算角度
                angle_rad = np.arctan2(y2 - y1, x2 - x1)
                angle_deg = np.degrees(angle_rad)

                # 将角度标准化到[-45, 45]范围
                if angle_deg > 45:
                    angle_deg -= 90
                elif angle_deg < -45:
                    angle_deg += 90
                elif angle_deg > 90:
                    angle_deg -= 180
                elif angle_deg < -90:
                    angle_deg += 180

                # 修正角度方向：右倾斜为负，左倾斜为正
                angle_deg = -angle_deg

                # 只考虑在检测范围内的角度
                if abs(angle_deg) <= self.max_detection_angle:
                    # 按长度加权：长线段权重更高
                    weight = length / 100.0  # 标准化权重
                    weighted_angles.append((angle_deg, weight))

            if not weighted_angles:
                logger.warning("未找到有效的角度")
                return 0.0, 0.0

            # 使用加权角度聚类找到主导角度
            dominant_angle, confidence = self._find_dominant_angle_weighted(weighted_angles)

            return dominant_angle, confidence

        except Exception as e:
            logger.error(f"角度检测失败: {str(e)}")
            return 0.0, 0.0

    def _find_dominant_angle(self, angles: List[float]) -> Tuple[float, float]:
        """
        找到主导角度（忽略异常值如歪斜的印章）

        Args:
            angles: 角度列表

        Returns:
            (主导角度, 置信度)
        """
        if not angles:
            return 0.0, 0.0

        # 将角度量化到0.25度精度，更精细的聚类
        quantized_angles = [round(angle * 4) / 4 for angle in angles]

        # 统计角度出现频率
        angle_counter = Counter(quantized_angles)

        # 找到出现频率最高的角度
        most_common = angle_counter.most_common(5)  # 取前5个最常见的角度

        if not most_common:
            return 0.0, 0.0

        # 尝试找到非零的主导角度
        dominant_angle = 0.0
        dominant_count = 0
        total_count = len(angles)

        for angle, count in most_common:
            if abs(angle) >= 0.25:  # 优先选择非零角度
                dominant_angle = angle
                dominant_count = count
                break

        # 如果没有找到非零角度，使用最常见的角度
        if dominant_count == 0:
            dominant_angle = most_common[0][0]
            dominant_count = most_common[0][1]

        confidence = dominant_count / total_count

        # 降低置信度阈值，避免漏检明显倾斜的图像
        if confidence < 0.1:
            logger.warning(f"主导角度置信度过低: {confidence:.3f}")
            return 0.0, confidence

        logger.debug(f"角度统计: {most_common[:3]}, 主导角度: {dominant_angle}°, 置信度: {confidence:.3f}")

        return dominant_angle, confidence

    def _find_dominant_angle_weighted(self, weighted_angles: List[Tuple[float, float]]) -> Tuple[float, float]:
        """
        使用加权方式找到主导角度

        Args:
            weighted_angles: (角度, 权重) 的列表

        Returns:
            (主导角度, 置信度)
        """
        if not weighted_angles:
            return 0.0, 0.0

        # 将角度量化到0.5度精度，便于聚类
        angle_weights = {}
        total_weight = 0.0

        for angle, weight in weighted_angles:
            quantized_angle = round(angle * 2) / 2  # 0.5度精度
            if quantized_angle not in angle_weights:
                angle_weights[quantized_angle] = 0.0
            angle_weights[quantized_angle] += weight
            total_weight += weight

        if total_weight == 0:
            return 0.0, 0.0

        # 找到权重最高的角度
        dominant_angle = max(angle_weights.items(), key=lambda x: x[1])
        angle, weight = dominant_angle

        # 计算置信度：主导角度的权重占比
        confidence = weight / total_weight

        # 如果主导角度的权重占比太低，可能是噪声
        if confidence < 0.3:
            logger.warning(f"主导角度权重占比过低: {confidence:.3f}")
            return 0.0, confidence

        # 置信度说明：表示主导角度的线段长度占总长度的比例
        confidence_percent = confidence * 100
        logger.debug(f"主导角度: {angle:.2f}°, 置信度: {confidence:.3f} ({confidence_percent:.1f}%的线段支持此角度)")

        return angle, confidence

    def _preprocess_for_detection(self, image: np.ndarray) -> np.ndarray:
        """
        为检测预处理图像

        Args:
            image: 输入图像

        Returns:
            预处理后的图像
        """
        height, width = image.shape[:2]
        max_dim = max(height, width)

        # 倾斜检测需要一定精度，但不需要太高分辨率
        max_size = 1024

        if max_dim > max_size:
            ratio = max_size / max_dim
            new_width = int(width * ratio)
            new_height = int(height * ratio)

            resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
            logger.debug(f"图像缩放用于检测: {width}x{height} -> {new_width}x{new_height}")
            return resized

        return image

    def _rotate_image(self, image: np.ndarray, angle: float) -> np.ndarray:
        """
        旋转图像

        Args:
            image: 输入图像
            angle: 旋转角度（度）

        Returns:
            旋转后的图像
        """
        if abs(angle) < 0.01:  # 角度太小，不需要旋转
            return image

        height, width = image.shape[:2]
        center = (width // 2, height // 2)

        # 获取旋转矩阵
        rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)

        # 计算旋转后的图像尺寸
        cos_val = abs(rotation_matrix[0, 0])
        sin_val = abs(rotation_matrix[0, 1])
        new_width = int((height * sin_val) + (width * cos_val))
        new_height = int((height * cos_val) + (width * sin_val))

        # 调整旋转矩阵的平移部分
        rotation_matrix[0, 2] += (new_width / 2) - center[0]
        rotation_matrix[1, 2] += (new_height / 2) - center[1]

        # 智能选择填充颜色
        fill_color = self._get_smart_fill_color(image)

        # 执行旋转
        rotated = cv2.warpAffine(image, rotation_matrix, (new_width, new_height),
                                flags=cv2.INTER_LINEAR, borderMode=cv2.BORDER_CONSTANT,
                                borderValue=fill_color)

        return rotated

    async def _detect_single_file(self, file_path: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """检测单个文件的倾斜角度"""
        try:
            # 检查文件格式
            file_ext = Path(file_path).suffix.lower()
            if file_ext not in self.supported_formats:
                return {
                    "file_path": file_path,
                    "detected_angle": 0.0,
                    "confidence": 0.0,
                    "needs_correction": False,
                    "error": f"不支持的文件格式: {file_ext}"
                }

            # 读取图像
            image = cv2.imread(file_path)
            if image is None:
                return {
                    "file_path": file_path,
                    "detected_angle": 0.0,
                    "confidence": 0.0,
                    "needs_correction": False,
                    "error": "无法读取图像文件"
                }

            # 检测
            result = await self._detect_single_image(image, Path(file_path).name, options)
            result["file_path"] = file_path

            return result

        except Exception as e:
            logger.error(f"检测文件 {file_path} 失败: {str(e)}")
            return {
                "file_path": file_path,
                "detected_angle": 0.0,
                "confidence": 0.0,
                "needs_correction": False,
                "error": str(e)
            }

    async def _detect_folder(self, folder_path: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """检测文件夹中所有图像的倾斜角度"""
        try:
            folder = Path(folder_path)

            # 遍历文件夹，找到所有支持的图像文件
            image_files = []
            for file_path in folder.rglob("*"):
                if file_path.is_file() and file_path.suffix.lower() in self.supported_formats:
                    image_files.append(str(file_path))

            if not image_files:
                return {
                    "folder_path": folder_path,
                    "total_files": 0,
                    "skewed_files": [],
                    "normal_files": [],
                    "error_files": [],
                    "summary": "文件夹中没有找到支持的图像文件"
                }

            logger.info(f"在文件夹 {folder_path} 中找到 {len(image_files)} 个图像文件")

            # 检测每个文件
            skewed_files = []
            normal_files = []
            error_files = []

            for file_path in image_files:
                result = await self._detect_single_file(file_path, options)

                if "error" in result:
                    error_files.append(result)
                elif result["needs_correction"]:
                    skewed_files.append(result)
                else:
                    normal_files.append(result)

            # 统计结果
            total_files = len(image_files)
            skewed_count = len(skewed_files)

            logger.info(f"文件夹检测完成: 总计{total_files}个文件, 倾斜文件{skewed_count}个")

            return {
                "folder_path": folder_path,
                "total_files": total_files,
                "skewed_count": skewed_count,
                "skewed_files": skewed_files,
                "normal_files": normal_files,
                "error_files": error_files,
                "summary": f"检测完成: {total_files}个文件中有{skewed_count}个需要矫正"
            }

        except Exception as e:
            logger.error(f"检测文件夹 {folder_path} 失败: {str(e)}")
            return {
                "folder_path": folder_path,
                "total_files": 0,
                "skewed_files": [],
                "normal_files": [],
                "error_files": [],
                "error": str(e)
            }

    async def _correct_single_image(self, image: np.ndarray, image_name: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """矫正单张图像"""
        try:
            # 先检测角度
            detection_result = await self._detect_single_image(image, image_name, options)

            if "error" in detection_result:
                return detection_result

            angle = detection_result["detected_angle"]
            needs_correction = detection_result["needs_correction"]

            if not needs_correction:
                logger.info(f"图像 {image_name} 角度 {angle:.2f}° 小于阈值，跳过矫正")
                return {
                    "image_name": image_name,
                    "detected_angle": angle,
                    "correction_applied": False,
                    "corrected_image": image,  # 返回原图像
                    "message": "角度小于阈值，未进行矫正"
                }

            # 执行矫正
            corrected_image = self._rotate_image(image, -angle)  # 负角度矫正

            logger.info(f"图像 {image_name} 矫正完成，角度: {angle:.2f}°")

            return {
                "image_name": image_name,
                "detected_angle": angle,
                "correction_applied": True,
                "corrected_image": corrected_image,
                "original_size": image.shape[:2],
                "corrected_size": corrected_image.shape[:2]
            }

        except Exception as e:
            logger.error(f"矫正图像 {image_name} 失败: {str(e)}")
            return {
                "image_name": image_name,
                "detected_angle": 0.0,
                "correction_applied": False,
                "error": str(e)
            }

    async def _correct_single_file(self, file_path: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """矫正单个文件"""
        try:
            # 检查文件格式
            file_ext = Path(file_path).suffix.lower()
            if file_ext not in self.supported_formats:
                return {
                    "file_path": file_path,
                    "detected_angle": 0.0,
                    "correction_applied": False,
                    "error": f"不支持的文件格式: {file_ext}"
                }

            # 读取图像
            image = cv2.imread(file_path)
            if image is None:
                return {
                    "file_path": file_path,
                    "detected_angle": 0.0,
                    "correction_applied": False,
                    "error": "无法读取图像文件"
                }

            # 矫正图像
            result = await self._correct_single_image(image, Path(file_path).name, options)

            if "error" in result or not result.get("correction_applied", False):
                result["file_path"] = file_path
                return result

            # 生成保存路径 - 自动命名策略
            overwrite = options.get("overwrite", False)

            if overwrite:
                corrected_path = file_path
            else:
                corrected_path = self._generate_corrected_filename(file_path)

            # 保存矫正后的图像 - 使用统一的保存方法
            success = self.image_utils.save_image_to_path(result["corrected_image"], corrected_path)

            if not success:
                return {
                    "file_path": file_path,
                    "detected_angle": result["detected_angle"],
                    "correction_applied": False,
                    "error": "保存矫正后的图像失败"
                }

            logger.info(f"文件矫正完成: {file_path} -> {corrected_path}")

            return {
                "file_path": file_path,
                "corrected_file": corrected_path,
                "detected_angle": result["detected_angle"],
                "correction_applied": True,
                "original_size": result["original_size"],
                "corrected_size": result["corrected_size"]
            }

        except Exception as e:
            logger.error(f"矫正文件 {file_path} 失败: {str(e)}")
            return {
                "file_path": file_path,
                "detected_angle": 0.0,
                "correction_applied": False,
                "error": str(e)
            }

    async def _correct_folder(self, folder_path: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """矫正文件夹中的所有图像"""
        try:
            folder = Path(folder_path)

            # 遍历文件夹，找到所有支持的图像文件
            image_files = []
            for file_path in folder.rglob("*"):
                if file_path.is_file() and file_path.suffix.lower() in self.supported_formats:
                    image_files.append(str(file_path))

            if not image_files:
                return {
                    "folder_path": folder_path,
                    "total_files": 0,
                    "corrected_files": [],
                    "skipped_files": [],
                    "error_files": [],
                    "summary": "文件夹中没有找到支持的图像文件"
                }

            logger.info(f"开始矫正文件夹 {folder_path} 中的 {len(image_files)} 个图像文件")

            # 矫正每个文件
            corrected_files = []
            skipped_files = []
            error_files = []

            for file_path in image_files:
                result = await self._correct_single_file(file_path, options)

                if "error" in result:
                    error_files.append(result)
                elif result["correction_applied"]:
                    corrected_files.append(result)
                else:
                    skipped_files.append(result)

            # 统计结果
            total_files = len(image_files)
            corrected_count = len(corrected_files)
            skipped_count = len(skipped_files)
            error_count = len(error_files)

            logger.info(f"文件夹矫正完成: 总计{total_files}个文件, 矫正{corrected_count}个, 跳过{skipped_count}个, 错误{error_count}个")

            return {
                "folder_path": folder_path,
                "total_files": total_files,
                "corrected_count": corrected_count,
                "skipped_count": skipped_count,
                "error_count": error_count,
                "corrected_files": corrected_files,
                "skipped_files": skipped_files,
                "error_files": error_files,
                "summary": f"矫正完成: {total_files}个文件中矫正了{corrected_count}个"
            }

        except Exception as e:
            logger.error(f"矫正文件夹 {folder_path} 失败: {str(e)}")
            return {
                "folder_path": folder_path,
                "total_files": 0,
                "corrected_files": [],
                "skipped_files": [],
                "error_files": [],
                "error": str(e)
            }

    def _normalize_angle(self, angle: float) -> float:
        """
        标准化角度到[-45, 45]范围

        Args:
            angle: 输入角度

        Returns:
            标准化后的角度
        """
        # 将角度标准化到[-45, 45]范围
        while angle > 45:
            angle -= 90
        while angle < -45:
            angle += 90
        return angle

    def _generate_corrected_filename(self, original_path: str) -> str:
        """
        自动生成矫正后的文件名

        Args:
            original_path: 原始文件路径

        Returns:
            矫正后的文件路径
        """
        file_path_obj = Path(original_path).resolve()

        # 简单的命名：原名_corrected.扩展名
        corrected_name = f"{file_path_obj.stem}_corrected{file_path_obj.suffix}"
        corrected_path = str(file_path_obj.parent / corrected_name)

        # 如果文件已存在，删除它（说明之前矫正错了）
        if Path(corrected_path).exists():
            Path(corrected_path).unlink()

        return corrected_path

    def _get_smart_fill_color(self, image: np.ndarray) -> tuple:
        """
        智能选择填充颜色

        Args:
            image: 输入图像

        Returns:
            填充颜色 (B, G, R) 元组
        """
        height, width = image.shape[:2]

        # 提取图像边缘像素
        edge_pixels = []

        # 上边缘
        edge_pixels.extend(image[0, :].reshape(-1, image.shape[2] if len(image.shape) == 3 else 1))
        # 下边缘
        edge_pixels.extend(image[height-1, :].reshape(-1, image.shape[2] if len(image.shape) == 3 else 1))
        # 左边缘
        edge_pixels.extend(image[:, 0].reshape(-1, image.shape[2] if len(image.shape) == 3 else 1))
        # 右边缘
        edge_pixels.extend(image[:, width-1].reshape(-1, image.shape[2] if len(image.shape) == 3 else 1))

        edge_pixels = np.array(edge_pixels)

        # 如果是灰度图
        if len(image.shape) == 2:
            # 计算边缘像素的平均值
            avg_color = int(np.mean(edge_pixels))
            return (avg_color, avg_color, avg_color)

        # 彩色图像：计算每个通道的平均值
        avg_b = int(np.mean(edge_pixels[:, 0]))
        avg_g = int(np.mean(edge_pixels[:, 1]))
        avg_r = int(np.mean(edge_pixels[:, 2]))

        # 如果平均颜色接近白色（文档背景），使用白色
        if avg_b > 240 and avg_g > 240 and avg_r > 240:
            return (255, 255, 255)

        # 如果平均颜色太暗，可能是文字区域，使用白色
        if avg_b < 50 and avg_g < 50 and avg_r < 50:
            return (255, 255, 255)

        return (avg_b, avg_g, avg_r)
