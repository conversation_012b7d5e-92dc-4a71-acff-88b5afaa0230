"""
WebSocket路由模块
处理客户端的WebSocket连接和图像处理请求
"""

import json
import uuid
from typing import Dict, Any

from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from fastapi.websockets import WebSocketState

from app.services.image_processor import ImageProcessorService
from app.models.schemas import ProcessRequest, ProcessResponse
from app.utils.logger import setup_logger

logger = setup_logger(__name__)
router = APIRouter()

# 连接管理器
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
    
    async def connect(self, websocket: WebSocket, client_id: str):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        logger.info(f"客户端 {client_id} 已连接")
    
    def disconnect(self, client_id: str):
        """断开WebSocket连接"""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            logger.info(f"客户端 {client_id} 已断开连接")
    
    async def send_message(self, client_id: str, message: dict):
        """发送消息给指定客户端"""
        if client_id in self.active_connections:
            websocket = self.active_connections[client_id]
            if websocket.client_state == WebSocketState.CONNECTED:
                await websocket.send_text(json.dumps(message, ensure_ascii=False))
            else:
                self.disconnect(client_id)

# 全局连接管理器实例
manager = ConnectionManager()

# 图像处理服务实例
image_processor = ImageProcessorService()


@router.websocket("")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket主端点"""
    client_id = str(uuid.uuid4())
    
    try:
        await manager.connect(websocket, client_id)
        
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            
            try:
                # 解析请求
                request_data = json.loads(data)
                logger.info(f"收到客户端 {client_id} 的请求: {request_data.get('action', 'unknown')}")
                
                # 处理请求
                response = await handle_request(request_data)
                
                # 发送响应
                await manager.send_message(client_id, response)
                
            except json.JSONDecodeError:
                error_response = {
                    "task_id": None,
                    "status": "error",
                    "error": "无效的JSON格式"
                }
                await manager.send_message(client_id, error_response)
                
            except Exception as e:
                logger.error(f"处理请求时发生错误: {str(e)}")
                error_response = {
                    "task_id": request_data.get("task_id"),
                    "status": "error",
                    "error": f"服务器内部错误: {str(e)}"
                }
                await manager.send_message(client_id, error_response)
    
    except WebSocketDisconnect:
        manager.disconnect(client_id)
    except Exception as e:
        logger.error(f"WebSocket连接错误: {str(e)}")
        manager.disconnect(client_id)


async def handle_request(request_data: dict) -> dict:
    """处理客户端请求"""
    task_id = request_data.get("task_id", str(uuid.uuid4()))
    action = request_data.get("action")
    params = request_data.get("params", {})
    
    try:
        if action == "process_image":
            # 处理图像
            result = await image_processor.process_image(params)
            
            return {
                "task_id": task_id,
                "status": "success",
                "result": result
            }
        
        elif action == "ping":
            # 心跳检测
            return {
                "task_id": task_id,
                "status": "success",
                "result": {"message": "pong"}
            }
        
        else:
            return {
                "task_id": task_id,
                "status": "error",
                "error": f"不支持的操作: {action}"
            }
    
    except Exception as e:
        logger.error(f"处理请求失败: {str(e)}")
        return {
            "task_id": task_id,
            "status": "error",
            "error": str(e)
        }
