"""
边框处理功能测试
"""

import pytest
import asyncio
import os
import cv2
import numpy as np
from pathlib import Path

# 将项目根目录添加到Python路径
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.services.border_handler import BorderHandler
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

# 定义测试资源路径
TEST_IMAGE_FOLDER = "testimage"


@pytest.fixture(scope="module")
def event_loop():
    """为pytest-asyncio提供事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="module")
def border_handler():
    """初始化边框处理器"""
    return BorderHandler()


@pytest.fixture(scope="module")
def test_image():
    """获取测试图像"""
    if not os.path.exists(TEST_IMAGE_FOLDER):
        pytest.skip(f"测试图片文件夹不存在: {TEST_IMAGE_FOLDER}")
    
    # 找到第一个图像文件
    for filename in os.listdir(TEST_IMAGE_FOLDER):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            image_path = os.path.join(TEST_IMAGE_FOLDER, filename)
            image = cv2.imread(image_path)
            if image is not None:
                return image
    
    pytest.skip("测试文件夹中没有找到有效的图像文件")


@pytest.mark.asyncio
async def test_add_border_default(border_handler: BorderHandler, test_image: np.ndarray):
    """测试默认补边"""
    logger.info("测试默认补边")
    
    original_height, original_width = test_image.shape[:2]
    result = await border_handler.add_border(test_image)
    
    print(f"\n默认补边结果:")
    print(f"  原始尺寸: {original_width}x{original_height}")
    print(f"  新尺寸: {result.shape[1]}x{result.shape[0]}")
    
    # 默认是10像素白边
    assert result.shape[0] == original_height + 20  # 上下各10
    assert result.shape[1] == original_width + 20   # 左右各10


@pytest.mark.asyncio
async def test_add_border_pixels(border_handler: BorderHandler, test_image: np.ndarray):
    """测试像素单位补边"""
    logger.info("测试像素单位补边")
    
    options = {
        'border_size': 50,
        'unit': 'pixels',
        'color_mode': 'white'
    }
    
    original_height, original_width = test_image.shape[:2]
    result = await border_handler.add_border(test_image, options)
    
    print(f"\n像素补边结果:")
    print(f"  原始尺寸: {original_width}x{original_height}")
    print(f"  新尺寸: {result.shape[1]}x{result.shape[0]}")
    print(f"  补边大小: 50像素")
    
    assert result.shape[0] == original_height + 100  # 上下各50
    assert result.shape[1] == original_width + 100   # 左右各50


@pytest.mark.asyncio
async def test_add_border_mm(border_handler: BorderHandler, test_image: np.ndarray):
    """测试毫米单位补边"""
    logger.info("测试毫米单位补边")
    
    options = {
        'border_size': 10,  # 10毫米
        'unit': 'mm',
        'dpi': 300,
        'color_mode': 'white'
    }
    
    original_height, original_width = test_image.shape[:2]
    result = await border_handler.add_border(test_image, options)
    
    # 10mm在300DPI下约为118像素
    expected_pixels = int(10 / 25.4 * 300)
    
    print(f"\n毫米补边结果:")
    print(f"  原始尺寸: {original_width}x{original_height}")
    print(f"  新尺寸: {result.shape[1]}x{result.shape[0]}")
    print(f"  补边大小: 10mm (约{expected_pixels}像素)")
    
    assert abs(result.shape[0] - (original_height + 2 * expected_pixels)) <= 2
    assert abs(result.shape[1] - (original_width + 2 * expected_pixels)) <= 2


@pytest.mark.asyncio
async def test_target_size(border_handler: BorderHandler, test_image: np.ndarray):
    """测试目标尺寸补边"""
    logger.info("测试目标尺寸补边")
    
    original_height, original_width = test_image.shape[:2]
    target_width = original_width + 200
    target_height = original_height + 150
    
    options = {
        'target_size': (target_width, target_height),
        'unit': 'pixels',
        'color_mode': 'white'
    }
    
    result = await border_handler.add_border(test_image, options)
    
    print(f"\n目标尺寸补边结果:")
    print(f"  原始尺寸: {original_width}x{original_height}")
    print(f"  目标尺寸: {target_width}x{target_height}")
    print(f"  新尺寸: {result.shape[1]}x{result.shape[0]}")
    
    assert result.shape[1] == target_width
    assert result.shape[0] == target_height


@pytest.mark.asyncio
async def test_different_color_modes(border_handler: BorderHandler, test_image: np.ndarray):
    """测试不同颜色模式"""
    logger.info("测试不同颜色模式")
    
    color_modes = ["white", "black", "edge_color", "dominant_color"]
    
    for color_mode in color_modes:
        options = {
            'border_size': 30,
            'unit': 'pixels',
            'color_mode': color_mode
        }
        
        result = await border_handler.add_border(test_image, options)
        
        print(f"\n{color_mode} 模式结果:")
        print(f"  新尺寸: {result.shape[1]}x{result.shape[0]}")
        
        # 检查边框颜色
        if color_mode == "white":
            # 检查顶部边框是否为白色
            top_border = result[0, result.shape[1]//2]
            assert np.allclose(top_border, [255, 255, 255], atol=5)
        elif color_mode == "black":
            # 检查顶部边框是否为黑色
            top_border = result[0, result.shape[1]//2]
            assert np.allclose(top_border, [0, 0, 0], atol=5)


@pytest.mark.asyncio
async def test_custom_color(border_handler: BorderHandler, test_image: np.ndarray):
    """测试自定义颜色"""
    logger.info("测试自定义颜色")
    
    custom_color = (0, 255, 0)  # 绿色 (BGR格式)
    
    options = {
        'border_size': 25,
        'unit': 'pixels',
        'color_mode': 'custom',
        'custom_color': custom_color
    }
    
    result = await border_handler.add_border(test_image, options)
    
    print(f"\n自定义颜色结果:")
    print(f"  设定颜色: {custom_color}")
    print(f"  新尺寸: {result.shape[1]}x{result.shape[0]}")
    
    # 检查边框颜色
    top_border = result[0, result.shape[1]//2]
    assert np.allclose(top_border, custom_color, atol=5)


@pytest.mark.asyncio
async def test_asymmetric_border(border_handler: BorderHandler, test_image: np.ndarray):
    """测试不对称补边"""
    logger.info("测试不对称补边")
    
    # (上, 右, 下, 左)
    border_size = (20, 40, 30, 10)
    
    options = {
        'border_size': border_size,
        'unit': 'pixels',
        'color_mode': 'white'
    }
    
    original_height, original_width = test_image.shape[:2]
    result = await border_handler.add_border(test_image, options)
    
    print(f"\n不对称补边结果:")
    print(f"  设定补边: 上{border_size[0]}, 右{border_size[1]}, 下{border_size[2]}, 左{border_size[3]}")
    print(f"  原始尺寸: {original_width}x{original_height}")
    print(f"  新尺寸: {result.shape[1]}x{result.shape[0]}")
    
    expected_width = original_width + border_size[1] + border_size[3]  # 右 + 左
    expected_height = original_height + border_size[0] + border_size[2]  # 上 + 下
    
    assert result.shape[1] == expected_width
    assert result.shape[0] == expected_height


@pytest.mark.asyncio
async def test_remove_border_manual(border_handler: BorderHandler, test_image: np.ndarray):
    """测试手动移除边框"""
    logger.info("测试手动移除边框")
    
    # 先添加边框
    add_options = {
        'border_size': 50,
        'unit': 'pixels',
        'color_mode': 'white'
    }
    bordered_image = await border_handler.add_border(test_image, add_options)
    
    # 手动移除边框
    remove_options = {
        'method': 'manual',
        'crop_rect': (50, 50, test_image.shape[1], test_image.shape[0])  # (x, y, width, height)
    }
    
    result = await border_handler.remove_border(bordered_image, remove_options)
    
    print(f"\n手动移除边框结果:")
    print(f"  原始尺寸: {test_image.shape[1]}x{test_image.shape[0]}")
    print(f"  添加边框后: {bordered_image.shape[1]}x{bordered_image.shape[0]}")
    print(f"  移除边框后: {result.shape[1]}x{result.shape[0]}")
    
    # 应该恢复到原始尺寸
    assert result.shape[0] == test_image.shape[0]
    assert result.shape[1] == test_image.shape[1]


if __name__ == "__main__":
    """直接运行边框处理测试"""
    import asyncio
    
    async def run_border_tests():
        """运行边框处理测试"""
        border_handler = BorderHandler()
        
        print(f"🚀 开始边框处理功能测试...")
        print(f"📁 测试文件夹: {TEST_IMAGE_FOLDER}")
        
        if not os.path.exists(TEST_IMAGE_FOLDER):
            print(f"❌ 测试文件夹不存在: {TEST_IMAGE_FOLDER}")
            return
        
        # 找到测试图像
        test_image = None
        for filename in os.listdir(TEST_IMAGE_FOLDER):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                image_path = os.path.join(TEST_IMAGE_FOLDER, filename)
                test_image = cv2.imread(image_path)
                if test_image is not None:
                    break
        
        if test_image is None:
            print(f"❌ 测试文件夹中没有找到有效的图像文件")
            return
        
        print(f"📷 使用测试图像尺寸: {test_image.shape[1]}x{test_image.shape[0]}")
        
        # 运行各种测试
        print(f"\n" + "="*60)
        await test_add_border_default(border_handler, test_image)
        
        print(f"\n" + "="*60)
        await test_add_border_pixels(border_handler, test_image)
        
        print(f"\n" + "="*60)
        await test_add_border_mm(border_handler, test_image)
        
        print(f"\n" + "="*60)
        await test_target_size(border_handler, test_image)
        
        print(f"\n" + "="*60)
        await test_different_color_modes(border_handler, test_image)
        
        print(f"\n" + "="*60)
        await test_custom_color(border_handler, test_image)
        
        print(f"\n" + "="*60)
        await test_asymmetric_border(border_handler, test_image)
        
        print(f"\n" + "="*60)
        await test_remove_border_manual(border_handler, test_image)
        
        print(f"\n✅ 边框处理功能测试完成!")
    
    # 运行测试
    asyncio.run(run_border_tests())
