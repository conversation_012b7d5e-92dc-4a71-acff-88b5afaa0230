"""
配置管理模块
管理应用的配置参数
"""

import os
from typing import List
from pydantic import ConfigDict
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置"""

    # 基础配置
    APP_NAME: str = "ImageChecked"
    VERSION: str = "1.0.0"
    DEBUG: bool = False

    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 11113

    # CORS配置
    ALLOWED_ORIGINS: List[str] = ["*"]

    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"

    # 图像处理配置
    MAX_IMAGE_SIZE: int = 50 * 1024 * 1024  # 50MB
    SUPPORTED_FORMATS: List[str] = ["jpg", "jpeg", "png", "bmp", "tiff", "tif"]

    # 使用新的 Pydantic V2 配置方式
    model_config = ConfigDict(
        env_file=".env",
        case_sensitive=True
    )


# 全局配置实例
settings = Settings()
