"""
倾斜检测和矫正功能测试
"""

import pytest
import asyncio
from pathlib import Path
from app.services.skew_corrector import SkewCorrector


class TestSkewCorrector:
    """倾斜检测和矫正测试类"""
    
    @pytest.fixture
    def corrector(self):
        """创建倾斜矫正器实例"""
        return SkewCorrector()
    
    @pytest.fixture
    def test_image_path(self):
        """测试图像路径"""
        return "testimage/16.jpg"  # 已知倾斜的图像
    
    @pytest.mark.asyncio
    async def test_detect_skew(self, corrector, test_image_path):
        """测试倾斜检测功能"""
        result = await corrector.detect(test_image_path)
        
        # 验证返回结果结构
        assert "detected_angle" in result
        assert "confidence" in result
        assert "needs_correction" in result
        
        # 验证检测到倾斜
        assert abs(result["detected_angle"]) > 0
        assert result["needs_correction"] is True
        assert result["confidence"] > 0.1
    
    @pytest.mark.asyncio
    async def test_correct_skew(self, corrector, test_image_path):
        """测试倾斜矫正功能（自动命名）"""
        result = await corrector.correct(test_image_path)  # 不再指定后缀，使用自动命名

        # 验证返回结果结构
        assert "file_path" in result
        assert "detected_angle" in result
        assert "correction_applied" in result

        # 验证矫正成功
        assert result["correction_applied"] is True
        assert "corrected_file" in result

        # 验证生成的文件存在
        corrected_file = result["corrected_file"]
        assert Path(corrected_file).exists()

        # 验证文件名格式：原名_corrected.扩展名
        original_path = Path(test_image_path)
        expected_name = f"{original_path.stem}_corrected{original_path.suffix}"
        assert expected_name in corrected_file
    
    @pytest.mark.asyncio
    async def test_folder_detect(self, corrector):
        """测试文件夹检测功能"""
        result = await corrector.detect("testimage")

        # 验证返回结果结构
        assert "total_files" in result
        assert "skewed_count" in result
        assert "skewed_files" in result
        assert "normal_files" in result
        assert "error_files" in result

        # 验证处理了文件
        assert result["total_files"] > 0
        assert result["skewed_count"] >= 0
        assert len(result["skewed_files"]) >= 0
    
    def test_angle_normalization(self, corrector):
        """测试角度标准化功能"""
        # 测试各种角度的标准化
        test_cases = [
            (0, 0),
            (45, 45),
            (-45, -45),
            (90, 0),    # 90度应该标准化为0度
            (-90, 0),   # -90度应该标准化为0度
            (135, 45),  # 135度应该标准化为45度
        ]

        for input_angle, expected in test_cases:
            normalized = corrector._normalize_angle(input_angle)
            assert abs(normalized - expected) < 0.1, f"角度 {input_angle} 标准化失败"

    @pytest.mark.asyncio
    async def test_weighted_detection_accuracy(self, corrector):
        """测试加权检测算法减少误检"""

        # 测试明显倾斜的文件
        result_16 = await corrector.detect("testimage/16.jpg")
        assert result_16["needs_correction"] is True, "16.jpg应该被检测为倾斜"
        assert abs(result_16["detected_angle"]) >= 2.0, "16.jpg的倾斜角度应该大于2度"

        # 测试正常文件（应该不需要矫正）
        normal_files = ["testimage/4.jpg", "testimage/6.jpg", "testimage/9.jpg"]
        for file_path in normal_files:
            result = await corrector.detect(file_path)
            # 正常文件不应该需要矫正
            assert result["needs_correction"] is False, f"{file_path} 不应该被检测为倾斜"

        # 测试批量检测，验证误检率降低
        batch_result = await corrector.detect("testimage")
        skewed_count = batch_result["skewed_count"]

        # 倾斜文件数量应该大幅减少（远少于之前的14个）
        assert skewed_count <= 3, f"倾斜文件数量过多: {skewed_count}，应该 <= 3"
        assert skewed_count >= 1, "应该至少检测到1个倾斜文件（16.jpg）"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
