"""
空白页检测模块
检测图像是否为空白页面
"""

import os
import cv2
import numpy as np
from typing import Tuple, Dict, Any, List, Union
from pathlib import Path

from app.utils.logger import setup_logger
from app.utils.image_utils import ImageUtils

logger = setup_logger(__name__)


class BlankDetector:
    """空白页检测器"""

    def __init__(self):
        self.image_utils = ImageUtils()

        # 检测参数
        self.edge_threshold_low = 50
        self.edge_threshold_high = 150
        self.edge_ratio_threshold = 0.0012    # 边缘像素比例阈值（介于0.001和0.0015之间）
        self.bright_ratio_threshold = 0.90    # 亮像素比例阈值
        self.bright_pixel_threshold = 240     # 亮像素阈值

        # 支持的图像格式
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}

    async def detect(self, input_data: Union[str, np.ndarray], options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        检测空白页

        Args:
            input_data: 输入数据，可以是：
                - 图像数组 (np.ndarray)
                - 文件路径 (str)
                - 文件夹路径 (str)
            options: 检测选项

        Returns:
            检测结果字典
        """
        if options is None:
            options = {}

        logger.info("开始空白页检测")

        # 判断输入类型
        if isinstance(input_data, np.ndarray):
            # 单张图像数组
            return await self._detect_single_image(input_data, "image_array", options)

        elif isinstance(input_data, str):
            # 文件或文件夹路径
            path = Path(input_data)

            if not path.exists():
                raise FileNotFoundError(f"路径不存在: {input_data}")

            if path.is_file():
                # 单个文件
                return await self._detect_single_file(str(path), options)

            elif path.is_dir():
                # 文件夹
                return await self._detect_folder(str(path), options)

        else:
            raise ValueError(f"不支持的输入类型: {type(input_data)}")

    async def _detect_single_image(self, image: np.ndarray, image_name: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """检测单张图像"""
        try:
            # 预处理：如果图像太大，缩小以提高检测速度
            processed_image = self._preprocess_for_detection(image)

            # 转换为灰度图
            if len(processed_image.shape) == 3:
                gray = cv2.cvtColor(processed_image, cv2.COLOR_BGR2GRAY)
            else:
                gray = processed_image

            # 1. 边缘检测
            edges = cv2.Canny(gray, self.edge_threshold_low, self.edge_threshold_high)
            edge_pixels = np.sum(edges > 0)
            total_pixels = edges.size
            edge_ratio = edge_pixels / total_pixels

            # 2. 亮度统计
            bright_pixels = np.sum(gray > self.bright_pixel_threshold)
            bright_ratio = bright_pixels / total_pixels

            # 3. 综合判断
            is_blank = (edge_ratio < self.edge_ratio_threshold) and (bright_ratio > self.bright_ratio_threshold)

            # 计算置信度分数 (0-1，越接近1越可能是空白页)
            confidence = self._calculate_confidence(edge_ratio, bright_ratio)

            logger.info(f"图像 {image_name}: 边缘比例={edge_ratio:.4f}, 亮度比例={bright_ratio:.4f}, 是否空白={is_blank}")

            return {
                "image_name": image_name,
                "is_blank": is_blank,
                "confidence": confidence,
                "edge_ratio": edge_ratio,
                "bright_ratio": bright_ratio,
                "image_size": image.shape[:2]
            }

        except Exception as e:
            logger.error(f"检测图像 {image_name} 失败: {str(e)}")
            return {
                "image_name": image_name,
                "is_blank": False,
                "confidence": 0.0,
                "error": str(e)
            }

    async def _detect_single_file(self, file_path: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """检测单个文件"""
        try:
            # 检查文件格式
            file_ext = Path(file_path).suffix.lower()
            if file_ext not in self.supported_formats:
                return {
                    "file_path": file_path,
                    "is_blank": False,
                    "confidence": 0.0,
                    "error": f"不支持的文件格式: {file_ext}"
                }

            # 读取图像
            image = cv2.imread(file_path)
            if image is None:
                return {
                    "file_path": file_path,
                    "is_blank": False,
                    "confidence": 0.0,
                    "error": "无法读取图像文件"
                }

            # 检测
            result = await self._detect_single_image(image, Path(file_path).name, options)
            result["file_path"] = file_path

            return result

        except Exception as e:
            logger.error(f"检测文件 {file_path} 失败: {str(e)}")
            return {
                "file_path": file_path,
                "is_blank": False,
                "confidence": 0.0,
                "error": str(e)
            }

    async def _detect_folder(self, folder_path: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """检测文件夹中的所有图像"""
        try:
            folder = Path(folder_path)

            # 遍历文件夹，找到所有支持的图像文件
            image_files = []
            for file_path in folder.rglob("*"):
                if file_path.is_file() and file_path.suffix.lower() in self.supported_formats:
                    image_files.append(str(file_path))

            if not image_files:
                return {
                    "folder_path": folder_path,
                    "total_files": 0,
                    "blank_files": [],
                    "non_blank_files": [],
                    "error_files": [],
                    "summary": "文件夹中没有找到支持的图像文件"
                }

            logger.info(f"在文件夹 {folder_path} 中找到 {len(image_files)} 个图像文件")

            # 检测每个文件
            blank_files = []
            non_blank_files = []
            error_files = []

            for file_path in image_files:
                result = await self._detect_single_file(file_path, options)

                if "error" in result:
                    error_files.append(result)
                elif result["is_blank"]:
                    blank_files.append(result)
                else:
                    non_blank_files.append(result)

            # 统计结果
            total_files = len(image_files)
            blank_count = len(blank_files)

            logger.info(f"文件夹检测完成: 总计{total_files}个文件, 空白页{blank_count}个")

            return {
                "folder_path": folder_path,
                "total_files": total_files,
                "blank_count": blank_count,
                "blank_files": blank_files,
                "non_blank_files": non_blank_files,
                "error_files": error_files,
                "summary": f"检测完成: {total_files}个文件中有{blank_count}个空白页"
            }

        except Exception as e:
            logger.error(f"检测文件夹 {folder_path} 失败: {str(e)}")
            return {
                "folder_path": folder_path,
                "total_files": 0,
                "blank_files": [],
                "non_blank_files": [],
                "error_files": [],
                "error": str(e)
            }

    def _preprocess_for_detection(self, image: np.ndarray) -> np.ndarray:
        """
        为检测预处理图像（缩小尺寸以提高速度）

        Args:
            image: 输入图像

        Returns:
            预处理后的图像
        """
        height, width = image.shape[:2]
        max_dim = max(height, width)

        # 空白页检测不需要太高精度，缩小图像提高速度
        max_size = 1024

        if max_dim > max_size:
            ratio = max_size / max_dim
            new_width = int(width * ratio)
            new_height = int(height * ratio)

            # 使用INTER_AREA进行缩小
            resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
            logger.debug(f"图像缩放用于检测: {width}x{height} -> {new_width}x{new_height}")
            return resized

        return image

    def _calculate_confidence(self, edge_ratio: float, bright_ratio: float) -> float:
        """
        计算空白页置信度

        Args:
            edge_ratio: 边缘像素比例
            bright_ratio: 亮像素比例

        Returns:
            置信度 (0-1，越接近1越可能是空白页)
        """
        # 边缘评分：边缘越少，评分越高
        if edge_ratio <= self.edge_ratio_threshold:
            # 在阈值内，线性映射到0.5-1.0
            edge_score = 0.5 + 0.5 * (1.0 - edge_ratio / self.edge_ratio_threshold)
        else:
            # 超过阈值，快速衰减
            excess = edge_ratio - self.edge_ratio_threshold
            edge_score = max(0.0, 0.5 * np.exp(-excess * 100))

        # 亮度评分：亮度越高，评分越高
        if bright_ratio >= self.bright_ratio_threshold:
            # 在阈值以上，映射到0.5-1.0
            bright_score = 0.5 + 0.5 * (bright_ratio - self.bright_ratio_threshold) / (1.0 - self.bright_ratio_threshold)
        else:
            # 在阈值以下，线性衰减
            bright_score = 0.5 * bright_ratio / self.bright_ratio_threshold

        # 综合评分：使用加权平均，边缘权重更高
        confidence = 0.6 * edge_score + 0.4 * bright_score
        return min(1.0, max(0.0, confidence))

    def get_detection_summary(self, results: Dict[str, Any]) -> str:
        """
        获取检测结果摘要

        Args:
            results: 检测结果

        Returns:
            摘要字符串
        """
        if "folder_path" in results:
            # 文件夹检测结果
            total = results.get("total_files", 0)
            blank = results.get("blank_count", 0)
            errors = len(results.get("error_files", []))

            summary = f"文件夹检测完成:\n"
            summary += f"- 总文件数: {total}\n"
            summary += f"- 空白页数: {blank}\n"
            summary += f"- 有效页数: {total - blank - errors}\n"

            if errors > 0:
                summary += f"- 错误文件: {errors}\n"

            return summary

        elif "file_path" in results:
            # 单文件检测结果
            is_blank = results.get("is_blank", False)
            confidence = results.get("confidence", 0.0)

            status = "空白页" if is_blank else "非空白页"
            return f"检测结果: {status} (置信度: {confidence:.2f})"

        else:
            # 单图像检测结果
            is_blank = results.get("is_blank", False)
            confidence = results.get("confidence", 0.0)

            status = "空白页" if is_blank else "非空白页"
            return f"检测结果: {status} (置信度: {confidence:.2f})"
