"""
边框处理模块
为图像添加或移除边框，支持像素和毫米单位，智能颜色选择
"""

import cv2
import numpy as np
from typing import Dict, Any

from app.utils.logger import setup_logger

logger = setup_logger(__name__)


class BorderHandler:
    """边框处理器"""

    def __init__(self):
        self.default_dpi = 300  # 默认DPI

    async def add_border(self, image: np.ndarray, options: Dict[str, Any] = None) -> np.ndarray:
        """
        为图像添加边框

        Args:
            image: 输入图像
            options: 配置选项
                - target_size: 目标尺寸 (width, height)
                - border_size: 边框大小，支持:
                    - int: 四边相同
                    - tuple(int, int): (水平, 垂直)
                    - tuple(int, int, int, int): (上, 右, 下, 左)
                - unit: 单位 ("pixels" 或 "mm")
                - dpi: DPI设置 (当unit为mm时使用)
                - color_mode: 颜色模式 ("white", "black", "edge_color", "dominant_color", "custom")
                - custom_color: 自定义颜色 (B, G, R)

        Returns:
            添加边框后的图像
        """
        if options is None:
            options = {}

        try:
            original_height, original_width = image.shape[:2]

            # 获取参数
            target_size = options.get('target_size')
            border_size = options.get('border_size')
            unit = options.get('unit', 'pixels')
            dpi = options.get('dpi', self.default_dpi)
            color_mode = options.get('color_mode', 'white')
            custom_color = options.get('custom_color')

            # 计算补边参数
            if target_size:
                target_width, target_height = target_size
                if unit == "mm":
                    target_width = self._mm_to_pixels(target_width, dpi)
                    target_height = self._mm_to_pixels(target_height, dpi)

                padding = self._calculate_padding_for_target_size(
                    original_width, original_height, target_width, target_height
                )
            elif border_size:
                padding = self._parse_border_size(border_size, unit, dpi)
            else:
                # 默认添加10像素白边
                padding = {"top": 10, "bottom": 10, "left": 10, "right": 10}

            # 计算补边颜色
            border_color = self._calculate_border_color(image, color_mode, custom_color)

            # 应用补边
            bordered_image = cv2.copyMakeBorder(
                image,
                padding['top'], padding['bottom'],
                padding['left'], padding['right'],
                cv2.BORDER_CONSTANT,
                value=border_color
            )

            logger.info(f"添加边框成功: {original_width}x{original_height} -> {bordered_image.shape[1]}x{bordered_image.shape[0]}")
            return bordered_image

        except Exception as e:
            logger.error(f"添加边框失败: {str(e)}")
            return image

    async def remove_border(self, image: np.ndarray, options: Dict[str, Any] = None) -> np.ndarray:
        """
        移除图像边框

        Args:
            image: 输入图像
            options: 配置选项
                - method: 检测方法 ("edge_detection", "color_threshold", "manual")
                - threshold: 边缘检测阈值
                - border_color: 要移除的边框颜色
                - crop_rect: 手动裁剪区域 (x, y, width, height)

        Returns:
            移除边框后的图像
        """
        if options is None:
            options = {}

        try:
            method = options.get('method', 'edge_detection')

            if method == 'manual':
                # 手动裁剪
                crop_rect = options.get('crop_rect')
                if crop_rect:
                    x, y, w, h = crop_rect
                    return image[y:y+h, x:x+w]

            elif method == 'color_threshold':
                # 基于颜色阈值移除边框
                return self._remove_border_by_color(image, options)

            else:
                # 基于边缘检测移除边框
                return self._remove_border_by_edge_detection(image, options)

        except Exception as e:
            logger.error(f"移除边框失败: {str(e)}")
            return image

    def _mm_to_pixels(self, mm: float, dpi: int) -> int:
        """毫米转像素"""
        inches = mm / 25.4  # 1英寸 = 25.4毫米
        pixels = int(inches * dpi)
        return pixels

    def _calculate_padding_for_target_size(self, original_width: int, original_height: int,
                                         target_width: int, target_height: int) -> Dict[str, int]:
        """计算达到目标尺寸所需的补边"""
        if target_width < original_width or target_height < original_height:
            raise ValueError("目标尺寸不能小于原始尺寸")

        width_diff = target_width - original_width
        height_diff = target_height - original_height

        # 居中补边
        left = width_diff // 2
        right = width_diff - left
        top = height_diff // 2
        bottom = height_diff - top

        return {"top": top, "bottom": bottom, "left": left, "right": right}

    def _parse_border_size(self, border_size, unit: str, dpi: int) -> Dict[str, int]:
        """解析边框大小参数"""
        if isinstance(border_size, int):
            # 四边相同
            size = border_size
            if unit == "mm":
                size = self._mm_to_pixels(size, dpi)
            return {"top": size, "bottom": size, "left": size, "right": size}

        elif isinstance(border_size, tuple):
            if len(border_size) == 2:
                # (水平, 垂直)
                horizontal, vertical = border_size
                if unit == "mm":
                    horizontal = self._mm_to_pixels(horizontal, dpi)
                    vertical = self._mm_to_pixels(vertical, dpi)
                return {"top": vertical, "bottom": vertical, "left": horizontal, "right": horizontal}

            elif len(border_size) == 4:
                # (上, 右, 下, 左)
                top, right, bottom, left = border_size
                if unit == "mm":
                    top = self._mm_to_pixels(top, dpi)
                    right = self._mm_to_pixels(right, dpi)
                    bottom = self._mm_to_pixels(bottom, dpi)
                    left = self._mm_to_pixels(left, dpi)
                return {"top": top, "bottom": bottom, "left": left, "right": right}

        raise ValueError("border_size格式错误")

    def _calculate_border_color(self, image: np.ndarray, color_mode: str, custom_color) -> tuple:
        """计算补边颜色"""
        if color_mode == "white":
            return (255, 255, 255)
        elif color_mode == "black":
            return (0, 0, 0)
        elif color_mode == "custom" and custom_color:
            return custom_color
        elif color_mode == "edge_color":
            return self._get_edge_color(image)
        elif color_mode == "dominant_color":
            return self._get_dominant_color(image)
        else:
            # 默认白色
            return (255, 255, 255)

    def _get_edge_color(self, image: np.ndarray) -> tuple:
        """获取图像边缘的平均颜色"""
        height, width = image.shape[:2]

        # 提取边缘像素
        edge_pixels = []

        # 上边缘
        edge_pixels.extend(image[0, :].reshape(-1, 3))
        # 下边缘
        edge_pixels.extend(image[height-1, :].reshape(-1, 3))
        # 左边缘 (排除角落避免重复)
        edge_pixels.extend(image[1:height-1, 0].reshape(-1, 3))
        # 右边缘 (排除角落避免重复)
        edge_pixels.extend(image[1:height-1, width-1].reshape(-1, 3))

        edge_pixels = np.array(edge_pixels)

        # 计算平均颜色
        avg_color = np.mean(edge_pixels, axis=0)
        return tuple(map(int, avg_color))

    def _get_dominant_color(self, image: np.ndarray) -> tuple:
        """获取图像的主导颜色"""
        # 缩小图像以提高计算速度
        small_image = cv2.resize(image, (100, 100))
        pixels = small_image.reshape(-1, 3)

        # 简单的主导颜色计算 - 使用直方图
        # 将颜色空间量化以减少计算复杂度
        quantized = (pixels // 32) * 32  # 量化到32的倍数

        # 找到最常见的颜色
        unique_colors, counts = np.unique(quantized.reshape(-1, 3), axis=0, return_counts=True)
        dominant_idx = np.argmax(counts)
        dominant_color = unique_colors[dominant_idx]

        return tuple(map(int, dominant_color))

    def _remove_border_by_edge_detection(self, image: np.ndarray, options: Dict[str, Any]) -> np.ndarray:
        """基于边缘检测移除边框"""
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # 边缘检测
            edges = cv2.Canny(gray, 50, 150)

            # 查找轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if contours:
                # 找到最大的轮廓
                largest_contour = max(contours, key=cv2.contourArea)

                # 获取边界矩形
                x, y, w, h = cv2.boundingRect(largest_contour)

                # 添加一些边距以避免过度裁剪
                margin = options.get('margin', 5)
                x = max(0, x - margin)
                y = max(0, y - margin)
                w = min(image.shape[1] - x, w + 2 * margin)
                h = min(image.shape[0] - y, h + 2 * margin)

                # 裁剪图像
                cropped = image[y:y+h, x:x+w]
                return cropped

            return image

        except Exception as e:
            logger.error(f"边缘检测移除边框失败: {str(e)}")
            return image

    def _remove_border_by_color(self, image: np.ndarray, options: Dict[str, Any]) -> np.ndarray:
        """基于颜色阈值移除边框"""
        try:
            border_color = options.get('border_color', (255, 255, 255))  # 默认白色
            threshold = options.get('threshold', 30)

            # 创建掩码
            mask = np.all(np.abs(image - border_color) <= threshold, axis=2)

            # 找到非边框区域
            coords = np.where(~mask)
            if len(coords[0]) > 0:
                y_min, y_max = coords[0].min(), coords[0].max()
                x_min, x_max = coords[1].min(), coords[1].max()

                # 裁剪图像
                cropped = image[y_min:y_max+1, x_min:x_max+1]
                return cropped

            return image

        except Exception as e:
            logger.error(f"颜色阈值移除边框失败: {str(e)}")
            return image
