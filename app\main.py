"""
ImageChecked - 图像处理服务主入口
基于FastAPI和WebSocket的图像处理服务
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.api.websocket import router as websocket_router
from app.api.border_padding import router as border_padding_router
from app.core.config import settings
from app.utils.logger import setup_logger

# 设置日志
logger = setup_logger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="ImageChecked",
    description="图像处理服务 - 支持重复页检测、空白页检测、倾斜矫正等功能",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(websocket_router, prefix="/ws", tags=["websocket"])
app.include_router(border_padding_router, prefix="/api", tags=["border-padding"])


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("ImageChecked服务启动中...")
    logger.info(f"服务配置: {settings.dict()}")


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("ImageChecked服务关闭")


@app.get("/")
async def root():
    """根路径 - 健康检查"""
    return {
        "message": "ImageChecked图像处理服务",
        "version": "1.0.0",
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy"}


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
