# ImageChecked

一个基于FastAPI和OpenCV的图像处理服务，支持WebSocket长连接通信。

## 功能特性

- **图像相似度检测** - 支持文件夹扫描和自动相似度分组
- 空白页检测  
- 图像倾斜矫正
- 图像边框处理
- 折角修复
- 长图像拼接

## 技术栈

- **后端框架**: FastAPI + WebSocket
- **图像处理**: OpenCV 4.10+ + NumPy
- **Python版本**: 3.9+ (支持Windows 7魔改版/Windows 10+)
- **部署方式**: PyInstaller打包exe

## 项目结构

```
ImageChecked/
├── app/
│   ├── main.py                 # FastAPI应用入口
│   ├── api/
│   │   └── websocket.py        # WebSocket路由
│   ├── core/
│   │   ├── config.py           # 配置管理
│   │   └── exceptions.py       # 自定义异常
│   ├── services/
│   │   ├── image_processor.py  # 图像处理服务
│   │   ├── duplicate_detector.py
│   │   ├── blank_detector.py
│   │   ├── skew_corrector.py
│   │   ├── border_handler.py
│   │   ├── corner_corrector.py
│   │   └── image_stitcher.py
│   ├── models/
│   │   └── schemas.py          # 数据模型
│   └── utils/
│       ├── image_utils.py
│       └── logger.py
└── tests/
```

## 安装和运行

1. 安装依赖
```bash
pip install -r requirements.txt
```

2. 运行服务
```bash
python run.py
# 或者
uvicorn app.main:app --host 0.0.0.0 --port 11113
```

3. 访问服务
- API服务: http://localhost:11113
- API文档: http://localhost:11113/docs
- WebSocket连接: ws://localhost:11113/ws

## WebSocket通信协议

### 客户端请求
```json
{
  "task_id": "uuid",
  "action": "process_image",
  "params": {
    "image_data": "base64_string",
    "operations": ["detect_blank", "correct_skew", "add_border"]
  }
}
```

#### 相似度检测请求示例
```json
{
  "task_id": "uuid",
  "action": "process_image",
  "params": {
    "operations": ["scan_folder_similarity"],
    "folder_path": "/path/to/images",
    "options": {
      "similarity_method": "hybrid",
      "similarity_threshold": 0.8
    }
  }
}
```

### 服务端响应
```json
{
  "task_id": "uuid",
  "status": "success|error|processing",
  "result": {
    "processed_image": "base64_string",
    "metadata": {...}
  }
}
```

## 端口说明

默认端口: **11113** - 选择此端口避免与常见服务冲突
- 可通过环境变量 `PORT=端口号` 修改
- 或直接修改 `app/core/config.py` 中的 `PORT` 配置

## 开发

- 代码格式化: `black .`
- 代码检查: `flake8 .`
- 运行测试: `pytest`

## 打包部署

```bash
pyinstaller --onefile --windowed app/main.py
```
